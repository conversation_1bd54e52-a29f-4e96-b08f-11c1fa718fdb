import { create } from 'zustand';
import { persist, createJSONStorage } from 'zustand/middleware';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { Transaction } from '@/types/transaction';

// Simple ID generator that doesn't rely on crypto
const generateId = () => {
  return Math.random().toString(36).substring(2, 15) + 
         Math.random().toString(36).substring(2, 15);
};

interface TransactionState {
  transactions: Transaction[];
  isLoading: boolean;
  addTransaction: (transaction: Omit<Transaction, 'id' | 'createdAt' | 'updatedAt'>) => Promise<void>;
  updateTransaction: (id: string, transaction: Partial<Transaction>) => Promise<void>;
  deleteTransaction: (id: string) => Promise<void>;
  getTransactionById: (id: string | undefined) => Transaction | undefined;
}

export const useTransactionStore = create<TransactionState>()(
  persist(
    (set, get) => ({
      transactions: [],
      isLoading: false,
      
      addTransaction: async (transaction) => {
        set({ isLoading: true });
        try {
          const now = new Date().toISOString();
          const newTransaction: Transaction = {
            id: generateId(), // Use our simple ID generator instead of nanoid
            amount: typeof transaction.amount === 'string' ? parseFloat(transaction.amount) : transaction.amount,
            description: transaction.description,
            category: transaction.category,
            date: transaction.date,
            type: transaction.type,
            notes: transaction.notes,
            receiptUrl: transaction.receiptUrl,
            createdAt: now,
            updatedAt: now
          };
          
          set((state) => ({
            transactions: [newTransaction, ...state.transactions],
            isLoading: false
          }));
        } catch (error) {
          console.error('Error adding transaction:', error);
          set({ isLoading: false });
          throw error;
        }
      },
      
      updateTransaction: async (id, updatedTransaction) => {
        set({ isLoading: true });
        try {
          set((state) => ({
            transactions: state.transactions.map((transaction) =>
              transaction.id === id
                ? { 
                    ...transaction, 
                    ...updatedTransaction,
                    updatedAt: new Date().toISOString()
                  }
                : transaction
            ),
            isLoading: false
          }));
        } catch (error) {
          console.error('Error updating transaction:', error);
          set({ isLoading: false });
          throw error;
        }
      },
      
      deleteTransaction: async (id) => {
        set({ isLoading: true });
        try {
          set((state) => ({
            transactions: state.transactions.filter(
              (transaction) => transaction.id !== id
            ),
            isLoading: false
          }));
        } catch (error) {
          console.error('Error deleting transaction:', error);
          set({ isLoading: false });
          throw error;
        }
      },
      
      getTransactionById: (id) => {
        if (!id) return undefined;
        return get().transactions.find((transaction) => transaction.id === id);
      },
    }),
    {
      name: 'transaction-storage',
      storage: createJSONStorage(() => AsyncStorage),
    }
  )
);