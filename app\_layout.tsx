import FontAwesome from "@expo/vector-icons/FontAwesome";
import { useFonts } from "expo-font";
import { Stack, useRouter, useSegments } from "expo-router";
import * as SplashScreen from "expo-splash-screen";
import { useEffect } from "react";
import { StyleSheet } from "react-native";
import { GestureHandlerRootView } from "react-native-gesture-handler";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";

import { ErrorBoundary } from "./error-boundary";
import { ThemeProvider, useTheme } from "@/context/ThemeContext";
import { StatusBar } from "expo-status-bar";
import { AnalyticsProvider } from "@/context/AnalyticsContext";
import { BiometricAuthProvider } from "@/context/BiometricAuthContext";
import { NetworkProvider } from "@/context/NetworkContext";
import { ToastProvider } from "@/context/ToastContext";
import { trpc, trpcClient } from "@/lib/trpc";
import { AuthProvider, useAuth } from "@/context/AuthContext";

export const unstable_settings = {
  // Ensure that reloading on `/modal` keeps a back button present.
  initialRouteName: "login",
};

// Prevent the splash screen from auto-hiding before asset loading is complete.
SplashScreen.preventAutoHideAsync();

// Create a client
const queryClient = new QueryClient();

export default function RootLayout() {
  const [loaded, error] = useFonts({
    ...FontAwesome.font,
  });

  useEffect(() => {
    if (error) {
      console.error(error);
      throw error;
    }
  }, [error]);

  useEffect(() => {
    if (loaded) {
      SplashScreen.hideAsync();
    }
  }, [loaded]);

  if (!loaded) {
    return null;
  }

  return (
    <ErrorBoundary>
      <trpc.Provider client={trpcClient} queryClient={queryClient}>
        <QueryClientProvider client={queryClient}>
          <AuthProvider>
            <AnalyticsProvider>
              <NetworkProvider>
                <ThemeProvider>
                  <BiometricAuthProvider>
                    <ToastProvider>
                      <GestureHandlerRootView style={{ flex: 1 }}>
                        <AuthenticationGuard>
                          <RootLayoutNav />
                        </AuthenticationGuard>
                      </GestureHandlerRootView>
                    </ToastProvider>
                  </BiometricAuthProvider>
                </ThemeProvider>
              </NetworkProvider>
            </AnalyticsProvider>
          </AuthProvider>
        </QueryClientProvider>
      </trpc.Provider>
    </ErrorBoundary>
  );
}

// Authentication guard component
function AuthenticationGuard({ children }: { children: React.ReactNode }) {
  const { isAuthenticated, isLoading } = useAuth();
  const segments = useSegments();
  const router = useRouter();

  useEffect(() => {
    if (isLoading) return;

    const inAuthGroup = segments[0] === '(tabs)';
    
    if (!isAuthenticated && inAuthGroup) {
      // Redirect to the login page if not authenticated
      router.replace('/login');
    } else if (isAuthenticated && segments[0] !== '(tabs)' && segments[0] !== 'modal' && segments[0] !== 'transaction' && segments[0] !== 'settings') {
      // Redirect to the main app if authenticated and not already there
      router.replace('/(tabs)');
    }
  }, [isAuthenticated, segments, isLoading]);

  return <>{children}</>;
}

function RootLayoutNav() {
  const { colors: themeColors } = useTheme();
  
  return (
    <>
      <StatusBar style="auto" />
      <Stack
        screenOptions={{
          headerBackTitle: "Back",
          headerStyle: {
            ...styles.headerStyle,
            backgroundColor: themeColors.background,
          },
          headerTintColor: themeColors.text,
          headerShadowVisible: false,
        }}
      >
        <Stack.Screen name="login" options={{ headerShown: false }} />
        <Stack.Screen name="(tabs)" options={{ headerShown: false }} />
        <Stack.Screen name="modal" options={{ presentation: "modal" }} />
        <Stack.Screen name="transaction/[id]" options={{ headerShown: true, title: "Transaction Details" }} />
        <Stack.Screen name="transaction/edit/[id]" options={{ headerShown: true, title: "Edit Transaction" }} />
        <Stack.Screen name="settings/categories" options={{ headerShown: true, title: "Categories" }} />
        <Stack.Screen name="settings/currency" options={{ headerShown: true, title: "Select Currency" }} />
        <Stack.Screen name="settings/budget" options={{ headerShown: true, title: "Budget Settings" }} />
        <Stack.Screen name="onboarding" options={{ headerShown: false, presentation: "modal" }} />
      </Stack>
    </>
  );
}

// Create a separate StyleSheet for header styles
const styles = StyleSheet.create({
  headerStyle: {
    borderBottomWidth: 0,
  },
});