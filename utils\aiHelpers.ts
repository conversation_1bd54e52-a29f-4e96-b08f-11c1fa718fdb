import { getCategory, getAllCategories } from '@/constants/categories';

/**
 * Parses transactions from AI-generated text
 * @param text The text response from the AI
 * @returns Array of parsed transactions
 */
export function parseTransactionsFromText(text: string): {
  amount: number;
  description: string;
  category: string;
  date: string;
  type: 'income' | 'expense';
}[] {
  const transactions: {
    amount: number;
    description: string;
    category: string;
    date: string;
    type: 'income' | 'expense';
  }[] = [];
  
  try {
    // Look for transaction blocks with markdown-style formatting
    // This regex matches numbered transaction blocks
    const transactionBlockRegex = /\d+\.\s+\*\*(Income|Expense)\s+Transaction:\*\*[\s\S]*?(?=\d+\.\s+\*\*|\Z)/g;
    
    const matches = text.match(transactionBlockRegex);
    
    if (matches && matches.length > 0) {
      for (const blockContent of matches) {
        // Determine transaction type
        const typeMatch = blockContent.match(/\*\*(Income|Expense)\s+Transaction/);
        if (!typeMatch) continue;
        
        const type = typeMatch[1].toLowerCase() as 'income' | 'expense';
        
        // Extract amount - handle different currency symbols
        const amountMatch = blockContent.match(/\*\*Amount:\*\*\s*[$£€]?(\d+(?:\.\d+)?)/);
        if (!amountMatch) continue;
        const amount = parseFloat(amountMatch[1]);
        
        // Extract description
        const descriptionMatch = blockContent.match(/\*\*Description:\*\*\s*([^\r\n]+)/);
        const description = descriptionMatch ? descriptionMatch[1].trim() : 'Unspecified';
        
        // Extract category
        const categoryMatch = blockContent.match(/\*\*Category:\*\*\s*([^\r\n]+)/);
        const category = categoryMatch ? categoryMatch[1].trim() : '';
        
        // Extract date
        const dateMatch = blockContent.match(/\*\*Date:\*\*\s*([^\r\n(]+)/);
        const date = dateMatch ? dateMatch[1].trim() : new Date().toISOString().split('T')[0];
        
        transactions.push({
          amount,
          description,
          category: mapCategoryNameToId(category, type),
          date,
          type
        });
      }
    }
    
    // If no transactions were found with the markdown format, try a more general approach
    if (transactions.length === 0) {
      // Look for mentions of spending or earning money
      const spendingRegex = /(?:spent|paid|bought|purchased|expense of)\s*[$£€]?(\d+(?:\.\d+)?)\s*(?:on|for)\s*([^,.]+)/gi;
      const earningRegex = /(?:earned|received|got|income of)\s*[$£€]?(\d+(?:\.\d+)?)\s*(?:from|for)\s*([^,.]+)/gi;
      
      let spendingMatch;
      while ((spendingMatch = spendingRegex.exec(text)) !== null) {
        const amount = parseFloat(spendingMatch[1]);
        const description = spendingMatch[2].trim();
        
        transactions.push({
          amount,
          description,
          category: 'other_expense',
          date: new Date().toISOString().split('T')[0],
          type: 'expense'
        });
      }
      
      let earningMatch;
      while ((earningMatch = earningRegex.exec(text)) !== null) {
        const amount = parseFloat(earningMatch[1]);
        const description = earningMatch[2].trim();
        
        transactions.push({
          amount,
          description,
          category: 'other_income',
          date: new Date().toISOString().split('T')[0],
          type: 'income'
        });
      }
    }
    
    console.log("Extracted transactions:", transactions);
  } catch (error) {
    console.error('Error in parseTransactionsFromText:', error);
  }
  
  return transactions;
}

/**
 * Map a category name to a category ID
 */
function mapCategoryNameToId(categoryName: string, type: 'income' | 'expense'): string {
  if (!categoryName) {
    return type === 'income' ? 'other_income' : 'other_expense';
  }
  
  const allCategories = getAllCategories();
  
  // First try exact match
  const exactMatch = allCategories.find(
    cat => cat.name.toLowerCase() === categoryName.toLowerCase() && cat.type === type
  );
  if (exactMatch) return exactMatch.id;
  
  // Try partial match
  const partialMatch = allCategories.find(
    cat => categoryName.toLowerCase().includes(cat.name.toLowerCase()) && cat.type === type
  );
  if (partialMatch) return partialMatch.id;
  
  // Special case for "Food & Dining" category
  if (categoryName.toLowerCase().includes('food') || 
      categoryName.toLowerCase().includes('dining') ||
      categoryName.toLowerCase().includes('coffee') ||
      categoryName.toLowerCase().includes('restaurant') ||
      categoryName.toLowerCase().includes('groceries')) {
    return 'food';
  }
  
  // Special case for "Entertainment" category
  if (categoryName.toLowerCase().includes('entertainment') ||
      categoryName.toLowerCase().includes('netflix') ||
      categoryName.toLowerCase().includes('subscription') ||
      categoryName.toLowerCase().includes('streaming') ||
      categoryName.toLowerCase().includes('movie') ||
      categoryName.toLowerCase().includes('game')) {
    return 'entertainment';
  }
  
  // Default categories
  return type === 'income' ? 'other_income' : 'other_expense';
}