import { getCategory, getAllCategories } from '@/constants/categories';

/**
 * Parses transactions from AI-generated text with enhanced extraction capabilities
 * @param text The text response from the AI
 * @returns Array of parsed transactions
 */
export function parseTransactionsFromText(text: string): {
  amount: number;
  description: string;
  category: string;
  date: string;
  type: 'income' | 'expense';
}[] {
  const transactions: {
    amount: number;
    description: string;
    category: string;
    date: string;
    type: 'income' | 'expense';
  }[] = [];

  try {
    console.log("Parsing text:", text);

    // Enhanced regex patterns for better transaction extraction
    const patterns = [
      // Pattern 1: Numbered transaction blocks with markdown formatting
      /(\d+)\.\s*\*\*(Income|Expense)\s+Transaction:\*\*[\s\S]*?(?=\d+\.\s*\*\*|\Z)/gi,

      // Pattern 2: Simple transaction blocks without numbers
      /\*\*(Income|Expense)\s+Transaction:\*\*[\s\S]*?(?=\*\*(?:Income|Expense)\s+Transaction|\Z)/gi,

      // Pattern 3: Bullet point format
      /[-•]\s*\*\*(Income|Expense)\s+Transaction:\*\*[\s\S]*?(?=[-•]\s*\*\*|\Z)/gi,
    ];

    let foundTransactions = false;

    // Try each pattern until we find transactions
    for (const pattern of patterns) {
      const matches = text.match(pattern);

      if (matches && matches.length > 0) {
        foundTransactions = true;
        console.log(`Found ${matches.length} transaction blocks with pattern`);

        for (const blockContent of matches) {
          const transaction = parseTransactionBlock(blockContent);
          if (transaction) {
            transactions.push(transaction);
          }
        }
        break; // Stop after finding transactions with first successful pattern
      }
    }

    // If no transactions were found with the markdown format, try enhanced fallback patterns
    if (!foundTransactions) {
      console.log("No structured transactions found, trying fallback patterns...");

      // Enhanced fallback patterns for natural language
      const fallbackPatterns = [
        // Pattern 1: "I spent $X on Y" variations
        /(?:I\s+)?(?:spent|paid|bought|purchased|cost|expense(?:\s+of)?)\s*[$£€¥₹]?(\d+(?:\.\d+)?)\s*(?:on|for|at)\s*([^,.!?]+?)(?:\s+(?:yesterday|today|last\s+\w+|on\s+\w+|\d{1,2}\/\d{1,2}(?:\/\d{2,4})?|\d{4}-\d{2}-\d{2}))?/gi,

        // Pattern 2: "I earned/received $X from Y" variations
        /(?:I\s+)?(?:earned|received|got|made|income(?:\s+of)?)\s*[$£€¥₹]?(\d+(?:\.\d+)?)\s*(?:from|for|at)\s*([^,.!?]+?)(?:\s+(?:yesterday|today|last\s+\w+|on\s+\w+|\d{1,2}\/\d{1,2}(?:\/\d{2,4})?|\d{4}-\d{2}-\d{2}))?/gi,

        // Pattern 3: "$X for Y" format
        /[$£€¥₹](\d+(?:\.\d+)?)\s+(?:for|on|at)\s+([^,.!?]+?)(?:\s+(?:yesterday|today|last\s+\w+|on\s+\w+|\d{1,2}\/\d{1,2}(?:\/\d{2,4})?|\d{4}-\d{2}-\d{2}))?/gi,

        // Pattern 4: "Y cost $X" format
        /([^,.!?]+?)\s+(?:cost|costs|was|were)\s*[$£€¥₹]?(\d+(?:\.\d+)?)(?:\s+(?:yesterday|today|last\s+\w+|on\s+\w+|\d{1,2}\/\d{1,2}(?:\/\d{2,4})?|\d{4}-\d{2}-\d{2}))?/gi,
      ];

      for (const pattern of fallbackPatterns) {
        let match;
        while ((match = pattern.exec(text)) !== null) {
          let amount: number;
          let description: string;
          let type: 'income' | 'expense' = 'expense';

          // Handle different pattern formats
          if (pattern.source.includes('earned|received|got|made|income')) {
            amount = parseFloat(match[1]);
            description = match[2]?.trim() || 'Income';
            type = 'income';
          } else if (pattern.source.includes('cost|costs|was|were')) {
            description = match[1]?.trim() || 'Expense';
            amount = parseFloat(match[2]);
            type = 'expense';
          } else {
            amount = parseFloat(match[1]);
            description = match[2]?.trim() || 'Transaction';
          }

          if (amount > 0 && description) {
            // Extract date from the full match if present
            const dateFromText = extractDateFromText(match[0]);

            transactions.push({
              amount,
              description: cleanDescription(description),
              category: inferCategoryFromDescription(description, type),
              date: dateFromText || new Date().toISOString().split('T')[0],
              type
            });
          }
        }
      }
    }

    console.log("Extracted transactions:", transactions);
  } catch (error) {
    console.error('Error in parseTransactionsFromText:', error);
  }

  return transactions;
}

/**
 * Parse a structured transaction block
 */
function parseTransactionBlock(blockContent: string): {
  amount: number;
  description: string;
  category: string;
  date: string;
  type: 'income' | 'expense';
} | null {
  try {
    // Determine transaction type
    const typeMatch = blockContent.match(/\*\*(Income|Expense)\s+Transaction/i);
    if (!typeMatch) return null;

    const type = typeMatch[1].toLowerCase() as 'income' | 'expense';

    // Extract amount - handle different currency symbols and formats
    const amountMatch = blockContent.match(/\*\*Amount:\*\*\s*[$£€¥₹]?(\d+(?:,\d{3})*(?:\.\d+)?)/i);
    if (!amountMatch) return null;
    const amount = parseFloat(amountMatch[1].replace(/,/g, ''));

    // Extract description
    const descriptionMatch = blockContent.match(/\*\*Description:\*\*\s*([^\r\n]+)/i);
    const description = descriptionMatch ? descriptionMatch[1].trim() : 'Unspecified';

    // Extract category
    const categoryMatch = blockContent.match(/\*\*Category:\*\*\s*([^\r\n]+)/i);
    const category = categoryMatch ? categoryMatch[1].trim() : '';

    // Extract date with enhanced parsing
    const dateMatch = blockContent.match(/\*\*Date:\*\*\s*([^\r\n(]+)/i);
    const dateStr = dateMatch ? dateMatch[1].trim() : '';
    const date = parseDateString(dateStr) || new Date().toISOString().split('T')[0];

    return {
      amount,
      description: cleanDescription(description),
      category: mapCategoryNameToId(category, type),
      date,
      type
    };
  } catch (error) {
    console.error('Error parsing transaction block:', error);
    return null;
  }
}

/**
 * Enhanced date parsing from text
 */
function parseDateString(dateStr: string): string | null {
  if (!dateStr) return null;

  const today = new Date();
  const currentYear = today.getFullYear();

  // Clean the date string
  dateStr = dateStr.toLowerCase().trim();

  // Handle relative dates
  if (dateStr.includes('today')) {
    return today.toISOString().split('T')[0];
  }

  if (dateStr.includes('yesterday')) {
    const yesterday = new Date(today);
    yesterday.setDate(yesterday.getDate() - 1);
    return yesterday.toISOString().split('T')[0];
  }

  if (dateStr.includes('last week')) {
    const lastWeek = new Date(today);
    lastWeek.setDate(lastWeek.getDate() - 7);
    return lastWeek.toISOString().split('T')[0];
  }

  // Handle specific date formats
  const dateFormats = [
    /(\d{4})-(\d{2})-(\d{2})/, // YYYY-MM-DD
    /(\d{2})\/(\d{2})\/(\d{4})/, // MM/DD/YYYY
    /(\d{2})\/(\d{2})\/(\d{2})/, // MM/DD/YY
    /(\d{1,2})\/(\d{1,2})/, // MM/DD (current year)
  ];

  for (const format of dateFormats) {
    const match = dateStr.match(format);
    if (match) {
      try {
        let year, month, day;

        if (format.source.includes('(\\d{4})')) {
          // YYYY-MM-DD format
          year = parseInt(match[1]);
          month = parseInt(match[2]);
          day = parseInt(match[3]);
        } else if (format.source.includes('(\\d{4})')) {
          // MM/DD/YYYY format
          month = parseInt(match[1]);
          day = parseInt(match[2]);
          year = parseInt(match[3]);
        } else if (format.source.includes('(\\d{2})')) {
          // MM/DD/YY format
          month = parseInt(match[1]);
          day = parseInt(match[2]);
          year = parseInt(match[3]) + 2000;
        } else {
          // MM/DD format (current year)
          month = parseInt(match[1]);
          day = parseInt(match[2]);
          year = currentYear;
        }

        const date = new Date(year, month - 1, day);
        if (!isNaN(date.getTime())) {
          return date.toISOString().split('T')[0];
        }
      } catch (error) {
        console.error('Error parsing date:', error);
      }
    }
  }

  return null;
}

/**
 * Extract date from transaction text
 */
function extractDateFromText(text: string): string | null {
  const datePatterns = [
    /yesterday/i,
    /today/i,
    /last\s+week/i,
    /\d{1,2}\/\d{1,2}(?:\/\d{2,4})?/,
    /\d{4}-\d{2}-\d{2}/,
  ];

  for (const pattern of datePatterns) {
    const match = text.match(pattern);
    if (match) {
      return parseDateString(match[0]);
    }
  }

  return null;
}

/**
 * Clean and normalize description text
 */
function cleanDescription(description: string): string {
  return description
    .replace(/\b(yesterday|today|last\s+\w+|on\s+\w+)\b/gi, '') // Remove date references
    .replace(/\s+/g, ' ') // Normalize whitespace
    .trim()
    .replace(/^(and|or|,|\.)\s*/i, '') // Remove leading conjunctions
    .replace(/\s*(and|or|,|\.)$/i, '') // Remove trailing conjunctions
    || 'Transaction';
}

/**
 * Infer category from description using enhanced keyword matching
 */
function inferCategoryFromDescription(description: string, type: 'income' | 'expense'): string {
  const desc = description.toLowerCase();

  if (type === 'expense') {
    // Food & Dining
    if (/\b(food|dining|restaurant|cafe|coffee|lunch|dinner|breakfast|groceries|grocery|supermarket|meal|pizza|burger|sushi|takeout|delivery)\b/.test(desc)) {
      return 'food';
    }

    // Transportation
    if (/\b(gas|fuel|petrol|uber|lyft|taxi|bus|train|metro|parking|car|vehicle|transport|flight|airline)\b/.test(desc)) {
      return 'transportation';
    }

    // Entertainment
    if (/\b(movie|cinema|netflix|spotify|entertainment|game|gaming|concert|show|theater|subscription|streaming)\b/.test(desc)) {
      return 'entertainment';
    }

    // Shopping
    if (/\b(shopping|clothes|clothing|shoes|amazon|store|mall|purchase|buy|bought)\b/.test(desc)) {
      return 'shopping';
    }

    // Utilities
    if (/\b(electricity|water|gas|internet|phone|utility|utilities|bill|bills)\b/.test(desc)) {
      return 'utilities';
    }

    // Healthcare
    if (/\b(doctor|hospital|medicine|pharmacy|health|medical|dentist|clinic)\b/.test(desc)) {
      return 'healthcare';
    }

    // Housing
    if (/\b(rent|mortgage|housing|apartment|house|home|property)\b/.test(desc)) {
      return 'housing';
    }

    return 'other_expense';
  } else {
    // Income categories
    if (/\b(salary|wage|paycheck|job|work|employment)\b/.test(desc)) {
      return 'salary';
    }

    if (/\b(freelance|contract|consulting|gig|project)\b/.test(desc)) {
      return 'freelance';
    }

    if (/\b(investment|dividend|stock|crypto|trading|profit)\b/.test(desc)) {
      return 'investments';
    }

    if (/\b(gift|bonus|refund|cashback)\b/.test(desc)) {
      return 'gifts';
    }

    return 'other_income';
  }
}

/**
 * Map a category name to a category ID with enhanced matching
 */
function mapCategoryNameToId(categoryName: string, type: 'income' | 'expense'): string {
  if (!categoryName) {
    return type === 'income' ? 'other_income' : 'other_expense';
  }

  const allCategories = getAllCategories();

  // First try exact match
  const exactMatch = allCategories.find(
    cat => cat.name.toLowerCase() === categoryName.toLowerCase() && cat.type === type
  );
  if (exactMatch) return exactMatch.id;

  // Try partial match
  const partialMatch = allCategories.find(
    cat => categoryName.toLowerCase().includes(cat.name.toLowerCase()) && cat.type === type
  );
  if (partialMatch) return partialMatch.id;

  // Use enhanced inference
  return inferCategoryFromDescription(categoryName, type);
}