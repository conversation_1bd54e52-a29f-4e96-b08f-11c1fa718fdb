import { getCurrencyByCode } from '@/constants/currencies';
import { useSettingsStore } from '@/store/settingsStore';

export const formatCurrency = (amount: number): string => {
  const { currencyCode } = useSettingsStore.getState();
  const currency = getCurrencyByCode(currencyCode);
  
  const formattedAmount = new Intl.NumberFormat('en-US', {
    minimumFractionDigits: currency.decimalPlaces,
    maximumFractionDigits: currency.decimalPlaces,
  }).format(amount);
  
  return currency.symbolPosition === 'before' 
    ? `${currency.symbol}${formattedAmount}`
    : `${formattedAmount} ${currency.symbol}`;
};

export const formatDate = (dateString: string): string => {
  const date = new Date(dateString);
  return new Intl.DateTimeFormat('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
  }).format(date);
};

export const getRelativeDate = (dateString: string): string => {
  const date = new Date(dateString);
  const now = new Date();
  const diffTime = Math.abs(now.getTime() - date.getTime());
  const diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24));
  
  if (diffDays === 0) {
    return 'Today';
  } else if (diffDays === 1) {
    return 'Yesterday';
  } else if (diffDays === 2) {
    return 'Day before yesterday';
  } else if (diffDays < 7) {
    return `${diffDays} days ago`;
  } else if (diffDays < 30) {
    const weeks = Math.floor(diffDays / 7);
    return `${weeks} ${weeks === 1 ? 'week' : 'weeks'} ago`;
  } else {
    return formatDate(dateString);
  }
};

export const getCurrentDateContext = (): string => {
  const now = new Date();
  
  const year = now.getFullYear();
  const month = now.toLocaleString('en-US', { month: 'long' });
  const day = now.getDate();
  const weekday = now.toLocaleString('en-US', { weekday: 'long' });
  
  // Yesterday
  const yesterday = new Date(now);
  yesterday.setDate(now.getDate() - 1);
  const yesterdayFormatted = formatDate(yesterday.toISOString());
  const yesterdayISO = yesterday.toISOString().split('T')[0];
  
  // Day before yesterday
  const dayBeforeYesterday = new Date(now);
  dayBeforeYesterday.setDate(now.getDate() - 2);
  const dayBeforeYesterdayFormatted = formatDate(dayBeforeYesterday.toISOString());
  const dayBeforeYesterdayISO = dayBeforeYesterday.toISOString().split('T')[0];
  
  // Last week
  const lastWeek = new Date(now);
  lastWeek.setDate(now.getDate() - 7);
  const lastWeekFormatted = formatDate(lastWeek.toISOString());
  const lastWeekISO = lastWeek.toISOString().split('T')[0];
  
  // Last month
  const lastMonth = new Date(now);
  lastMonth.setMonth(now.getMonth() - 1);
  const lastMonthFormatted = lastMonth.toLocaleString('en-US', { month: 'long' });
  
  // Tomorrow
  const tomorrow = new Date(now);
  tomorrow.setDate(now.getDate() + 1);
  const tomorrowFormatted = formatDate(tomorrow.toISOString());
  const tomorrowISO = tomorrow.toISOString().split('T')[0];
  
  // Next week
  const nextWeek = new Date(now);
  nextWeek.setDate(now.getDate() + 7);
  const nextWeekFormatted = formatDate(nextWeek.toISOString());
  const nextWeekISO = nextWeek.toISOString().split('T')[0];
  
  const todayISO = now.toISOString().split('T')[0];
  
  return `Current date: ${weekday}, ${month} ${day}, ${year}. 
Today is ${formatDate(now.toISOString())} (${todayISO}).
Yesterday was ${yesterdayFormatted} (${yesterdayISO}).
Day before yesterday was ${dayBeforeYesterdayFormatted} (${dayBeforeYesterdayISO}).
Last week was around ${lastWeekFormatted} (${lastWeekISO}).
Last month was ${lastMonthFormatted}.
Tomorrow will be ${tomorrowFormatted} (${tomorrowISO}).
Next week will be around ${nextWeekFormatted} (${nextWeekISO}).

When the user mentions relative dates, convert them to actual dates:
- "today" → ${todayISO}
- "yesterday" → ${yesterdayISO}
- "day before yesterday" → ${dayBeforeYesterdayISO}
- "last week" → approximately ${lastWeekISO}
- "next week" → approximately ${nextWeekISO}
- "this month" → the current month (${month})
- "last month" → the previous month (${lastMonthFormatted})

IMPORTANT: Always use the current year (${year}) for all dates unless explicitly specified otherwise.`;
};