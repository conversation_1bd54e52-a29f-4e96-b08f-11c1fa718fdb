export interface Category {
  id: string;
  name: string;
  color: string;
  type: 'income' | 'expense';
}

// Default categories
const defaultCategories: Category[] = [
  // Income categories
  { id: 'salary', name: 'Salary', color: '#10B981', type: 'income' },
  { id: 'freelance', name: 'Freelance', color: '#3B82F6', type: 'income' },
  { id: 'investments', name: 'Investments', color: '#8B5CF6', type: 'income' },
  { id: 'gifts', name: 'Gifts', color: '#EC4899', type: 'income' },
  { id: 'other_income', name: 'Other Income', color: '#6B7280', type: 'income' },
  
  // Expense categories
  { id: 'food', name: 'Food & Dining', color: '#EF4444', type: 'expense' },
  { id: 'transportation', name: 'Transportation', color: '#F97316', type: 'expense' },
  { id: 'housing', name: 'Housing & Rent', color: '#8B5CF6', type: 'expense' },
  { id: 'entertainment', name: 'Entertainment', color: '#EC4899', type: 'expense' },
  { id: 'shopping', name: 'Shopping', color: '#06B6D4', type: 'expense' },
  { id: 'utilities', name: 'Utilities', color: '#3B82F6', type: 'expense' },
  { id: 'healthcare', name: 'Healthcare', color: '#10B981', type: 'expense' },
  { id: 'other_expense', name: 'Other Expense', color: '#6B7280', type: 'expense' },
];

// Get all income categories
export const getIncomeCategories = (): Category[] => {
  return defaultCategories.filter(category => category.type === 'income');
};

// Get all expense categories
export const getExpenseCategories = (): Category[] => {
  return defaultCategories.filter(category => category.type === 'expense');
};

// Get all categories (both income and expense)
export const getAllCategories = (): Category[] => {
  // In a real app, you would combine default categories with custom categories from storage
  // For now, we'll just return the default categories
  return defaultCategories;
};

// Get a specific category by ID
export const getCategory = (id: string): Category => {
  const category = defaultCategories.find(category => category.id === id);
  
  if (!category) {
    // Return a default category if not found
    if (id.includes('income') || id === 'salary' || id === 'freelance' || id === 'investments' || id === 'gifts') {
      return { id: 'other_income', name: 'Other Income', color: '#6B7280', type: 'income' };
    } else {
      return { id: 'other_expense', name: 'Other Expense', color: '#6B7280', type: 'expense' };
    }
  }
  
  return category;
};