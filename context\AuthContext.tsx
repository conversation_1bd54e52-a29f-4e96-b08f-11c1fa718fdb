import React, { createContext, useContext, useState, useEffect } from 'react';
import * as WebBrowser from 'expo-web-browser';
import * as Google from 'expo-auth-session/providers/google';
import * as AppleAuthentication from 'expo-apple-authentication';
import { Platform } from 'react-native';
import { trpcClient } from '@/lib/trpc';
import { secureStore } from '@/utils/secureStorage';
import { STORAGE_KEYS } from '@/constants/app';

WebBrowser.maybeCompleteAuthSession();

// Define the shape of our user object
export interface User {
  id: string;
  email: string;
  name: string | null;
  photoUrl: string | null;
  provider: 'google' | 'apple' | 'anonymous';
}

// Define the shape of our auth context
interface AuthContextType {
  user: User | null;
  isLoading: boolean;
  isAuthenticated: boolean;
  signInWithGoogle: () => Promise<boolean>;
  signInWithApple: () => Promise<boolean>;
  signInAnonymously: () => Promise<boolean>;
  signOut: () => Promise<void>;
}

// Create the context with a default value
const AuthContext = createContext<AuthContextType>({
  user: null,
  isLoading: true,
  isAuthenticated: false,
  signInWithGoogle: async () => false,
  signInWithApple: async () => false,
  signInAnonymously: async () => false,
  signOut: async () => {},
});

// Create a provider component
export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  // Set up Google OAuth
  const [request, response, promptAsync] = Google.useAuthRequest({
    expoClientId: 'YOUR_EXPO_CLIENT_ID',
    iosClientId: 'YOUR_IOS_CLIENT_ID',
    androidClientId: 'YOUR_ANDROID_CLIENT_ID',
    webClientId: 'YOUR_WEB_CLIENT_ID',
  });

  // Load user from storage on mount
  useEffect(() => {
    const loadUser = async () => {
      try {
        const storedUser = await secureStore.getItem(STORAGE_KEYS.USER);
        if (storedUser) {
          setUser(JSON.parse(storedUser));
        }
      } catch (error) {
        console.error('Failed to load user from storage:', error);
      } finally {
        setIsLoading(false);
      }
    };

    loadUser();
  }, []);

  // Handle Google auth response
  useEffect(() => {
    if (response?.type === 'success') {
      const { authentication } = response;
      if (authentication?.accessToken) {
        handleGoogleToken(authentication.accessToken);
      }
    }
  }, [response]);

  // Process Google token
  const handleGoogleToken = async (accessToken: string) => {
    try {
      setIsLoading(true);
      // In a real app, you would send this token to your backend
      // For now, we'll simulate a successful login
      const userData = await trpcClient.auth.googleLogin.mutate({ token: accessToken });
      
      // For demo purposes, create a mock user
      const mockUser: User = {
        id: userData.id || 'google-user-id',
        email: userData.email || '<EMAIL>',
        name: userData.name || 'Google User',
        photoUrl: userData.photoUrl || null,
        provider: 'google',
      };
      
      await secureStore.setItem(STORAGE_KEYS.USER, JSON.stringify(mockUser));
      setUser(mockUser);
      return true;
    } catch (error) {
      console.error('Google authentication error:', error);
      return false;
    } finally {
      setIsLoading(false);
    }
  };

  // Sign in with Google
  const signInWithGoogle = async (): Promise<boolean> => {
    try {
      setIsLoading(true);
      await promptAsync();
      return true;
    } catch (error) {
      console.error('Google sign in error:', error);
      return false;
    }
  };

  // Sign in with Apple
  const signInWithApple = async (): Promise<boolean> => {
    if (Platform.OS !== 'ios') return false;
    
    try {
      setIsLoading(true);
      const credential = await AppleAuthentication.signInAsync({
        requestedScopes: [
          AppleAuthentication.AppleAuthenticationScope.FULL_NAME,
          AppleAuthentication.AppleAuthenticationScope.EMAIL,
        ],
      });

      // In a real app, you would send this credential to your backend
      // For now, we'll simulate a successful login
      const userData = await trpcClient.auth.appleLogin.mutate({ 
        identityToken: credential.identityToken || '',
        user: credential.user
      });
      
      // For demo purposes, create a mock user
      const mockUser: User = {
        id: userData.id || credential.user,
        email: userData.email || '<EMAIL>',
        name: userData.name || 'Apple User',
        photoUrl: null,
        provider: 'apple',
      };
      
      await secureStore.setItem(STORAGE_KEYS.USER, JSON.stringify(mockUser));
      setUser(mockUser);
      return true;
    } catch (error) {
      console.error('Apple authentication error:', error);
      return false;
    } finally {
      setIsLoading(false);
    }
  };

  // Sign in anonymously
  const signInAnonymously = async (): Promise<boolean> => {
    try {
      setIsLoading(true);
      
      // Create an anonymous user
      const anonymousUser: User = {
        id: 'anonymous-' + Date.now(),
        email: '<EMAIL>',
        name: 'Guest User',
        photoUrl: null,
        provider: 'anonymous',
      };
      
      await secureStore.setItem(STORAGE_KEYS.USER, JSON.stringify(anonymousUser));
      setUser(anonymousUser);
      return true;
    } catch (error) {
      console.error('Anonymous sign in error:', error);
      return false;
    } finally {
      setIsLoading(false);
    }
  };

  // Sign out
  const signOut = async (): Promise<void> => {
    try {
      setIsLoading(true);
      await secureStore.removeItem(STORAGE_KEYS.USER);
      setUser(null);
    } catch (error) {
      console.error('Sign out error:', error);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <AuthContext.Provider
      value={{
        user,
        isLoading,
        isAuthenticated: !!user,
        signInWithGoogle,
        signInWithApple,
        signInAnonymously,
        signOut,
      }}
    >
      {children}
    </AuthContext.Provider>
  );
}

// Create a custom hook to use the auth context
export const useAuth = () => useContext(AuthContext);