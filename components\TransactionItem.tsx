import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Animated, Platform } from 'react-native';
import { useRouter } from 'expo-router';
import { Transaction } from '@/types/transaction';
import { formatCurrency } from '@/utils/formatters';
import { getCategory } from '@/constants/categories';
import { useTheme } from '@/context/ThemeContext';
import { Edit2, Trash2 } from 'lucide-react-native';
import * as Haptics from 'expo-haptics';

export interface TransactionItemProps {
  transaction: Transaction;
  onEdit?: (transaction: Transaction) => void;
  onDelete?: (id: string) => Promise<void>;
}

export default function TransactionItem({ transaction, onEdit, onDelete }: TransactionItemProps) {
  const router = useRouter();
  const { colors: themeColors } = useTheme();
  const scaleAnim = React.useRef(new Animated.Value(1)).current;
  
  const handlePress = () => {
    router.push(`/transaction/${transaction.id}`);
  };
  
  const handlePressIn = () => {
    Animated.spring(scaleAnim, {
      toValue: 0.98,
      friction: 8,
      tension: 100,
      useNativeDriver: true,
    }).start();
  };
  
  const handlePressOut = () => {
    Animated.spring(scaleAnim, {
      toValue: 1,
      friction: 8,
      tension: 40,
      useNativeDriver: true,
    }).start();
  };
  
  const handleEdit = () => {
    if (Platform.OS !== 'web') {
      Haptics.selectionAsync();
    }
    
    if (onEdit) {
      onEdit(transaction);
    } else {
      router.push(`/transaction/edit/${transaction.id}`);
    }
  };
  
  const handleDelete = async () => {
    if (Platform.OS !== 'web') {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
    }
    
    if (onDelete) {
      try {
        await onDelete(transaction.id);
      } catch (error) {
        console.error('Error deleting transaction:', error);
      }
    }
  };
  
  const formattedDate = new Date(transaction.date).toLocaleDateString(undefined, {
    month: 'short',
    day: 'numeric',
  });
  
  const category = getCategory(transaction.category);
  
  return (
    <Animated.View style={{ transform: [{ scale: scaleAnim }] }}>
      <TouchableOpacity
        style={[
          styles.container, 
          { 
            backgroundColor: themeColors.card,
            shadowColor: themeColors.text,
          }
        ]}
        onPress={handlePress}
        onPressIn={handlePressIn}
        onPressOut={handlePressOut}
        activeOpacity={0.9}
      >
        <View style={styles.leftSection}>
          <View 
            style={[
              styles.categoryIndicator, 
              { backgroundColor: category.color }
            ]} 
          />
          <View style={styles.detailsContainer}>
            <Text 
              style={[styles.description, { color: themeColors.text }]}
              numberOfLines={1}
            >
              {transaction.description}
            </Text>
            <Text style={[styles.categoryText, { color: themeColors.textLight }]}>
              {category.name} • {formattedDate}
            </Text>
          </View>
        </View>
        
        <View style={styles.rightSection}>
          <Text 
            style={[
              styles.amount, 
              { 
                color: transaction.type === 'expense' 
                  ? themeColors.expense 
                  : themeColors.income 
              }
            ]}
          >
            {transaction.type === 'expense' ? '-' : '+'}{formatCurrency(transaction.amount)}
          </Text>
          
          <View style={styles.actionsContainer}>
            {onEdit && (
              <TouchableOpacity 
                style={[styles.actionButton, { backgroundColor: themeColors.primary + '20' }]}
                onPress={handleEdit}
                hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
              >
                <Edit2 size={16} color={themeColors.primary} />
              </TouchableOpacity>
            )}
            
            {onDelete && (
              <TouchableOpacity 
                style={[styles.actionButton, { backgroundColor: themeColors.danger + '20' }]}
                onPress={handleDelete}
                hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
              >
                <Trash2 size={16} color={themeColors.danger} />
              </TouchableOpacity>
            )}
          </View>
        </View>
      </TouchableOpacity>
    </Animated.View>
  );
}

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderRadius: 12,
    marginBottom: 12,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.08,
    shadowRadius: 6,
    elevation: 3,
  },
  leftSection: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  categoryIndicator: {
    width: 12,
    height: 12,
    borderRadius: 6,
    marginRight: 12,
  },
  detailsContainer: {
    flex: 1,
  },
  description: {
    fontSize: 16,
    fontWeight: '500',
    marginBottom: 4,
  },
  categoryText: {
    fontSize: 14,
  },
  rightSection: {
    alignItems: 'flex-end',
  },
  amount: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 4,
  },
  actionsContainer: {
    flexDirection: 'row',
  },
  actionButton: {
    width: 28,
    height: 28,
    borderRadius: 14,
    justifyContent: 'center',
    alignItems: 'center',
    marginLeft: 8,
  },
});