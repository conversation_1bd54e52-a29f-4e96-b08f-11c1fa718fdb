import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { formatCurrency } from '@/utils/formatters';
import { ArrowUpRight, ArrowDownLeft } from 'lucide-react-native';
import { getCurrencyByCode } from '@/constants/currencies';
import { useSettingsStore } from '@/store/settingsStore';
import { useTheme } from '@/context/ThemeContext';

interface SummaryCardProps {
  income: number;
  expenses: number;
}

export default function SummaryCard({ income, expenses }: SummaryCardProps) {
  const balance = income - expenses;
  const isPositive = balance >= 0;
  const { currencyCode } = useSettingsStore();
  const currency = getCurrencyByCode(currencyCode);
  const { colors: themeColors } = useTheme();

  return (
    <View style={[styles.container, { backgroundColor: themeColors.card }]}>
      <View style={[styles.currencyBadge, { backgroundColor: themeColors.primary + '20' }]}>
        <Text style={[styles.currencyCode, { color: themeColors.primary }]}>{currency.code}</Text>
      </View>
      
      <View style={styles.balanceContainer}>
        <Text style={[styles.balanceLabel, { color: themeColors.textLight }]}>Current Balance</Text>
        <Text 
          style={[
            styles.balanceAmount, 
            { color: themeColors.text },
            !isPositive && { color: themeColors.expense }
          ]}
        >
          {formatCurrency(balance)}
        </Text>
      </View>
      
      <View style={styles.detailsContainer}>
        <View style={styles.detailItem}>
          <View style={[styles.iconContainer, { backgroundColor: themeColors.background }]}>
            <ArrowUpRight size={16} color={themeColors.income} />
          </View>
          <View>
            <Text style={[styles.detailLabel, { color: themeColors.textLight }]}>Income</Text>
            <Text style={[styles.detailAmount, { color: themeColors.text }]}>{formatCurrency(income)}</Text>
          </View>
        </View>
        
        <View style={[styles.separator, { backgroundColor: themeColors.border }]} />
        
        <View style={styles.detailItem}>
          <View style={[styles.iconContainer, { backgroundColor: themeColors.background }]}>
            <ArrowDownLeft size={16} color={themeColors.expense} />
          </View>
          <View>
            <Text style={[styles.detailLabel, { color: themeColors.textLight }]}>Expenses</Text>
            <Text style={[styles.detailAmount, { color: themeColors.text }]}>{formatCurrency(expenses)}</Text>
          </View>
        </View>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    borderRadius: 16,
    padding: 16,
    marginHorizontal: 16,
    marginBottom: 24,
    shadowColor: 'rgba(0, 0, 0, 0.1)',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 3,
    position: 'relative',
  },
  currencyBadge: {
    position: 'absolute',
    top: 12,
    right: 12,
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  currencyCode: {
    fontSize: 12,
    fontWeight: '600',
  },
  balanceContainer: {
    alignItems: 'center',
    marginBottom: 16,
  },
  balanceLabel: {
    fontSize: 14,
    marginBottom: 4,
  },
  balanceAmount: {
    fontSize: 28,
    fontWeight: '700',
  },
  detailsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  detailItem: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
  },
  iconContainer: {
    width: 32,
    height: 32,
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  detailLabel: {
    fontSize: 12,
    marginBottom: 2,
  },
  detailAmount: {
    fontSize: 16,
    fontWeight: '600',
  },
  separator: {
    width: 1,
    height: '100%',
    marginHorizontal: 16,
  },
});