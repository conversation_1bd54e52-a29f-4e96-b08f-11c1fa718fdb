import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TextInput,
  TouchableOpacity,
  ScrollView,
  KeyboardAvoidingView,
  Platform,
  Alert,
  ActivityIndicator
} from 'react-native';
import { useRouter } from 'expo-router';
import { useTransactionStore } from '@/store/transactionStore';
import TransactionTypeSwitch from '@/components/TransactionTypeSwitch';
import CategoryPicker from '@/components/CategoryPicker';
import * as ImagePicker from 'expo-image-picker';
import { Image } from 'expo-image';
import * as Haptics from 'expo-haptics';
import { Camera, X, Calendar } from 'lucide-react-native';
import { getCurrencyByCode } from '@/constants/currencies';
import { useSettingsStore } from '@/store/settingsStore';
import { TransactionType } from '@/types/transaction';
import { useTheme } from '@/context/ThemeContext';
import { useAnalytics } from '@/context/AnalyticsContext';
import { useToast } from '@/context/ToastContext';
import { useNetworkStatus } from '@/hooks/useNetworkStatus';
import { StatusBar } from 'expo-status-bar';

export default function AddTransactionScreen() {
  const router = useRouter();
  const { addTransaction, isLoading } = useTransactionStore();
  const { currencyCode } = useSettingsStore();
  const currency = getCurrencyByCode(currencyCode);
  const { theme, colors: themeColors } = useTheme();
  const { trackScreen, trackEvent } = useAnalytics();
  const { showToast } = useToast();
  const { isConnected } = useNetworkStatus();

  const [amount, setAmount] = useState('');
  const [description, setDescription] = useState('');
  const [category, setCategory] = useState('other');
  const [type, setType] = useState<TransactionType>('expense');
  const [date, setDate] = useState(new Date().toISOString().split('T')[0]);
  const [notes, setNotes] = useState('');
  const [receiptUrl, setReceiptUrl] = useState<string | undefined>();
  const [errors, setErrors] = useState<{[key: string]: string}>({});

  useEffect(() => {
    trackScreen('AddTransaction');
  }, []);

  const handleTypeChange = (newType: TransactionType) => {
    setType(newType);
    // Reset category when changing type
    setCategory(newType === 'income' ? 'salary' : 'other');
    trackEvent('change_transaction_type', { type: newType });
  };

  const validateForm = () => {
    const newErrors: {[key: string]: string} = {};

    if (!amount || parseFloat(amount) <= 0) {
      newErrors.amount = 'Please enter a valid amount greater than 0';
    }

    if (!description.trim()) {
      newErrors.description = 'Please enter a description';
    }

    if (!category) {
      newErrors.category = 'Please select a category';
    }

    // Validate date format (YYYY-MM-DD)
    const dateRegex = /^\d{4}-\d{2}-\d{2}$/;
    if (!date || !dateRegex.test(date)) {
      newErrors.date = 'Please enter a valid date (YYYY-MM-DD)';
    } else {
      // Check if date is in the future
      const selectedDate = new Date(date);
      const today = new Date();
      today.setHours(0, 0, 0, 0);

      if (selectedDate > today) {
        newErrors.date = 'Date cannot be in the future';
      }
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async () => {
    if (!validateForm()) {
      if (Platform.OS !== 'web') {
        Haptics.notificationAsync(Haptics.NotificationFeedbackType.Error);
      }
      return;
    }

    try {
      if (Platform.OS !== 'web') {
        Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
      }

      await addTransaction({
        amount: parseFloat(amount),
        description,
        category,
        date,
        type,
        notes,
        receiptUrl,
      });

      trackEvent('add_transaction', {
        type,
        category,
        has_receipt: !!receiptUrl,
        amount: parseFloat(amount)
      });

      showToast('Transaction added successfully');
      router.push('/');
    } catch (error) {
      console.error('Error adding transaction:', error);
      Alert.alert('Error', 'Failed to add transaction. Please try again.');
    }
  };

  const pickImage = async () => {
    if (Platform.OS !== 'web') {
      Haptics.selectionAsync();
    }

    try {
      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: true,
        aspect: [4, 3],
        quality: 0.8,
      });

      if (!result.canceled && result.assets && result.assets[0].uri) {
        setReceiptUrl(result.assets[0].uri);
        trackEvent('add_receipt_image');
      }
    } catch (error) {
      console.error('Error picking image:', error);
      Alert.alert('Error', 'Failed to pick image. Please try again.');
    }
  };

  const removeImage = () => {
    if (Platform.OS !== 'web') {
      Haptics.selectionAsync();
    }
    setReceiptUrl(undefined);
  };

  const setToday = () => {
    setDate(new Date().toISOString().split('T')[0]);
    setErrors(prev => ({...prev, date: ''}));
  };

  const setYesterday = () => {
    const yesterday = new Date();
    yesterday.setDate(yesterday.getDate() - 1);
    setDate(yesterday.toISOString().split('T')[0]);
    setErrors(prev => ({...prev, date: ''}));
  };

  return (
    <>
      <StatusBar style={theme === 'dark' ? 'light' : 'dark'} />

      <KeyboardAvoidingView
        style={[styles.container, { backgroundColor: themeColors.background }]}
        behavior={Platform.OS === 'ios' ? 'padding' : undefined}
        keyboardVerticalOffset={100}
      >
        {!isConnected && (
          <View style={[styles.offlineBanner, { backgroundColor: themeColors.warning + '30' }]}>
            <Text style={[styles.offlineText, { color: themeColors.warning }]}>
              You're offline. Transaction will be saved locally.
            </Text>
          </View>
        )}

        <ScrollView contentContainerStyle={styles.scrollContent}>
          <TransactionTypeSwitch value={type} onChange={handleTypeChange} />

          <View style={styles.formGroup}>
            <Text style={[styles.label, { color: themeColors.text }]}>Amount</Text>
            <View style={[
              styles.amountInputContainer,
              {
                backgroundColor: themeColors.card,
                borderColor: errors.amount ? themeColors.danger : themeColors.border
              }
            ]}>
              <Text style={[styles.currencySymbol, { color: themeColors.text }]}>{currency.symbol}</Text>
              <TextInput
                style={[styles.amountInput, { color: themeColors.text }]}
                placeholder="0.00"
                placeholderTextColor={themeColors.textLight}
                keyboardType="decimal-pad"
                value={amount}
                onChangeText={(text) => {
                  setAmount(text);
                  if (errors.amount) setErrors({...errors, amount: ''});
                }}
                accessibilityLabel="Transaction amount"
              />
            </View>
            {errors.amount ? (
              <Text style={[styles.errorText, { color: themeColors.danger }]}>{errors.amount}</Text>
            ) : null}
          </View>

          <View style={styles.formGroup}>
            <Text style={[styles.label, { color: themeColors.text }]}>Description</Text>
            <TextInput
              style={[
                styles.input,
                {
                  backgroundColor: themeColors.card,
                  color: themeColors.text,
                  borderColor: errors.description ? themeColors.danger : themeColors.border
                }
              ]}
              placeholder="What was this for?"
              placeholderTextColor={themeColors.textLight}
              value={description}
              onChangeText={(text) => {
                setDescription(text);
                if (errors.description) setErrors({...errors, description: ''});
              }}
              accessibilityLabel="Transaction description"
            />
            {errors.description ? (
              <Text style={[styles.errorText, { color: themeColors.danger }]}>{errors.description}</Text>
            ) : null}
          </View>

          <CategoryPicker
            selectedCategory={category}
            transactionType={type}
            onSelectCategory={(cat) => {
              setCategory(cat);
              if (errors.category) setErrors({...errors, category: ''});
            }}
          />
          {errors.category ? (
            <Text style={[styles.errorText, { color: themeColors.danger, marginTop: -16, marginBottom: 16 }]}>
              {errors.category}
            </Text>
          ) : null}

          <View style={styles.formGroup}>
            <Text style={[styles.label, { color: themeColors.text }]}>Date</Text>
            <View style={styles.dateContainer}>
              <TextInput
                style={[
                  styles.dateInput,
                  {
                    backgroundColor: themeColors.card,
                    color: themeColors.text,
                    borderColor: errors.date ? themeColors.danger : themeColors.border
                  }
                ]}
                placeholder="YYYY-MM-DD"
                placeholderTextColor={themeColors.textLight}
                value={date}
                onChangeText={(text) => {
                  setDate(text);
                  if (errors.date) setErrors({...errors, date: ''});
                }}
                accessibilityLabel="Transaction date"
              />
              <View style={styles.dateButtons}>
                <TouchableOpacity
                  style={[styles.dateButton, { backgroundColor: themeColors.card, borderColor: themeColors.border }]}
                  onPress={setToday}
                  accessibilityLabel="Set date to today"
                  accessibilityRole="button"
                >
                  <Text style={[styles.dateButtonText, { color: themeColors.primary }]}>Today</Text>
                </TouchableOpacity>
                <TouchableOpacity
                  style={[styles.dateButton, { backgroundColor: themeColors.card, borderColor: themeColors.border }]}
                  onPress={setYesterday}
                  accessibilityLabel="Set date to yesterday"
                  accessibilityRole="button"
                >
                  <Text style={[styles.dateButtonText, { color: themeColors.primary }]}>Yesterday</Text>
                </TouchableOpacity>
              </View>
            </View>
            {errors.date ? (
              <Text style={[styles.errorText, { color: themeColors.danger }]}>{errors.date}</Text>
            ) : null}
          </View>

          <View style={styles.formGroup}>
            <Text style={[styles.label, { color: themeColors.text }]}>Notes (Optional)</Text>
            <TextInput
              style={[
                styles.input,
                styles.notesInput,
                {
                  backgroundColor: themeColors.card,
                  color: themeColors.text,
                  borderColor: themeColors.border
                }
              ]}
              placeholder="Add any additional details..."
              placeholderTextColor={themeColors.textLight}
              value={notes}
              onChangeText={setNotes}
              multiline
              accessibilityLabel="Transaction notes"
            />
          </View>

          <View style={styles.formGroup}>
            <Text style={[styles.label, { color: themeColors.text }]}>Receipt (Optional)</Text>

            {receiptUrl ? (
              <View style={[styles.receiptContainer, { borderColor: themeColors.border }]}>
                <Image
                  source={{ uri: receiptUrl }}
                  style={styles.receiptImage}
                  contentFit="cover"
                  accessibilityLabel="Receipt image"
                />
                <TouchableOpacity
                  style={[styles.removeImageButton, { backgroundColor: themeColors.expense }]}
                  onPress={removeImage}
                  accessibilityLabel="Remove receipt image"
                  accessibilityRole="button"
                >
                  <X size={20} color={themeColors.background} />
                </TouchableOpacity>
              </View>
            ) : (
              <TouchableOpacity
                style={[
                  styles.uploadButton,
                  {
                    backgroundColor: themeColors.card,
                    borderColor: themeColors.border
                  }
                ]}
                onPress={pickImage}
                accessibilityLabel="Upload receipt"
                accessibilityRole="button"
              >
                <Camera size={24} color={themeColors.primary} />
                <Text style={[styles.uploadButtonText, { color: themeColors.primary }]}>Upload Receipt</Text>
              </TouchableOpacity>
            )}
          </View>

          <TouchableOpacity
            style={[
              styles.submitButton,
              {
                backgroundColor: isLoading ? themeColors.primary + '80' : themeColors.primary,
                opacity: isLoading ? 0.8 : 1
              }
            ]}
            onPress={handleSubmit}
            disabled={isLoading}
            accessibilityLabel="Save transaction"
            accessibilityRole="button"
          >
            {isLoading ? (
              <ActivityIndicator color={themeColors.background} size="small" />
            ) : (
              <Text style={[styles.submitButtonText, { color: themeColors.background }]}>Save Transaction</Text>
            )}
          </TouchableOpacity>
        </ScrollView>
      </KeyboardAvoidingView>
    </>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  offlineBanner: {
    padding: 8,
    alignItems: 'center',
  },
  offlineText: {
    fontSize: 14,
    fontWeight: '500',
  },
  scrollContent: {
    padding: 16,
  },
  formGroup: {
    marginBottom: 20,
  },
  label: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 8,
  },
  input: {
    borderRadius: 12,
    padding: 16,
    fontSize: 16,
    borderWidth: 1,
  },
  amountInputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    borderRadius: 12,
    borderWidth: 1,
    paddingHorizontal: 16,
  },
  currencySymbol: {
    fontSize: 24,
    fontWeight: '600',
    marginRight: 8,
  },
  amountInput: {
    flex: 1,
    padding: 16,
    fontSize: 24,
    fontWeight: '600',
  },
  dateContainer: {
    marginBottom: 8,
  },
  dateInput: {
    borderRadius: 12,
    padding: 16,
    fontSize: 16,
    borderWidth: 1,
    marginBottom: 8,
  },
  dateButtons: {
    flexDirection: 'row',
  },
  dateButton: {
    borderRadius: 8,
    paddingVertical: 8,
    paddingHorizontal: 12,
    marginRight: 8,
    borderWidth: 1,
  },
  dateButtonText: {
    fontSize: 14,
  },
  notesInput: {
    minHeight: 100,
    textAlignVertical: 'top',
  },
  uploadButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: 12,
    borderWidth: 1,
    borderStyle: 'dashed',
    padding: 20,
  },
  uploadButtonText: {
    fontSize: 16,
    fontWeight: '500',
    marginLeft: 8,
  },
  receiptContainer: {
    position: 'relative',
    borderRadius: 12,
    overflow: 'hidden',
    borderWidth: 1,
  },
  receiptImage: {
    width: '100%',
    height: 200,
    borderRadius: 12,
  },
  removeImageButton: {
    position: 'absolute',
    top: 8,
    right: 8,
    width: 32,
    height: 32,
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
  },
  submitButton: {
    borderRadius: 12,
    padding: 16,
    alignItems: 'center',
    marginTop: 16,
    marginBottom: 32,
  },
  submitButtonText: {
    fontSize: 16,
    fontWeight: '600',
  },
  errorText: {
    fontSize: 12,
    marginTop: 4,
  },
});