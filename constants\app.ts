// App version
export const APP_VERSION = '1.0.0';

// App constants
export const APP_NAME = 'Expense Tracker';

// Analytics constants
export const ANALYTICS_ENABLED = true;

// Security constants
export const BIOMETRIC_TIMEOUT_MS = 5 * 60 * 1000; // 5 minutes

// Storage keys
export const STORAGE_KEYS = {
  TRANSACTIONS: 'transactions-storage',
  SETTINGS: 'settings-storage',
  BUDGET: 'budget-storage',
  LAST_ACTIVE: 'last-active-time',
  ONBOARDING_SEEN: 'onboarding-seen',
  USER: 'user-data',
  AUTH_TOKEN: 'auth-token',
  REFRESH_TOKEN: 'refresh-token',
};

// API endpoints
export const API_ENDPOINTS = {
  BASE_URL: 'https://api.expensetracker.app',
  AUTH: '/auth',
  TRANSACTIONS: '/transactions',
  CATEGORIES: '/categories',
  BUDGET: '/budget',
};