import React, { useState, useRef } from 'react';
import {
  View,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  Platform,
  ActivityIndicator,
  Image,
  Animated,
} from 'react-native';
import { Send, Image as ImageIcon, X, Mic, Paperclip } from 'lucide-react-native';
import * as ImagePicker from 'expo-image-picker';
import { useTheme } from '@/context/ThemeContext';
import { LinearGradient } from 'expo-linear-gradient';

interface ChatInputProps {
  message: string;
  onChangeText: (text: string) => void;
  onSend: () => void;
  onImagePick: (uri: string) => void;
  onClearImage: () => void;
  image: string | null;
  isLoading: boolean;
  disabled?: boolean;
}

const ChatInput: React.FC<ChatInputProps> = ({
  message,
  onChangeText,
  onSend,
  onImagePick,
  onClearImage,
  image,
  isLoading,
  disabled = false,
}) => {
  const { theme, colors } = useTheme();
  const [isPickingImage, setIsPickingImage] = useState(false);
  const [inputHeight, setInputHeight] = useState(40);
  const scaleAnim = useRef(new Animated.Value(1)).current;
  const pulseAnim = useRef(new Animated.Value(1)).current;

  // Start pulse animation for the send button
  React.useEffect(() => {
    if (message.trim() || image) {
      Animated.loop(
        Animated.sequence([
          Animated.timing(pulseAnim, {
            toValue: 1.1,
            duration: 1000,
            useNativeDriver: true,
          }),
          Animated.timing(pulseAnim, {
            toValue: 1,
            duration: 1000,
            useNativeDriver: true,
          }),
        ])
      ).start();
    } else {
      pulseAnim.setValue(1);
    }
  }, [message, image]);

  const handlePickImage = async () => {
    setIsPickingImage(true);
    try {
      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: true,
        aspect: [4, 3],
        quality: 0.8,
      });

      if (!result.canceled && result.assets && result.assets.length > 0) {
        onImagePick(result.assets[0].uri);
      }
    } catch (error) {
      console.error('Error picking image:', error);
    } finally {
      setIsPickingImage(false);
    }
  };

  const handlePressIn = () => {
    Animated.spring(scaleAnim, {
      toValue: 0.95,
      friction: 8,
      tension: 100,
      useNativeDriver: true,
    }).start();
  };

  const handlePressOut = () => {
    Animated.spring(scaleAnim, {
      toValue: 1,
      friction: 8,
      tension: 40,
      useNativeDriver: true,
    }).start();
  };

  const handleContentSizeChange = (event: any) => {
    const { height } = event.nativeEvent.contentSize;
    setInputHeight(Math.min(Math.max(40, height), 100));
  };

  return (
    <View style={[
      styles.container,
      {
        borderTopColor: theme === 'dark' ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.05)',
        backgroundColor: theme === 'dark' ? colors.card : colors.background,
      }
    ]}>
      {image && (
        <View style={styles.imagePreviewContainer}>
          <Image source={{ uri: image }} style={styles.imagePreview} />
          <TouchableOpacity
            style={[styles.clearImageButton, { backgroundColor: colors.danger }]}
            onPress={onClearImage}
          >
            <X size={16} color="#FFFFFF" />
          </TouchableOpacity>
        </View>
      )}

      <View style={[
        styles.inputContainer,
        {
          backgroundColor: theme === 'dark' ? colors.aiBackground : colors.card,
          shadowColor: theme === 'dark' ? '#000' : '#888',
        }
      ]}>
        <View style={styles.inputActions}>
          <TouchableOpacity
            style={styles.imageButton}
            onPress={handlePickImage}
            disabled={isLoading || disabled || isPickingImage}
          >
            {isPickingImage ? (
              <ActivityIndicator size="small" color={colors.primary} />
            ) : (
              <ImageIcon
                size={22}
                color={disabled ? colors.textLight : colors.primary}
                style={{ opacity: disabled ? 0.5 : 0.8 }}
              />
            )}
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.attachButton}
            disabled={isLoading || disabled}
          >
            <Paperclip
              size={22}
              color={disabled ? colors.textLight : colors.primary}
              style={{ opacity: disabled ? 0.5 : 0.7 }}
            />
          </TouchableOpacity>
        </View>

        <TextInput
          style={[
            styles.input,
            {
              color: colors.text,
              height: inputHeight,
              maxHeight: 100,
              backgroundColor: theme === 'dark' ? 'rgba(0,0,0,0.2)' : 'rgba(255,255,255,0.8)',
            },
            Platform.OS === 'web' && styles.inputWeb,
          ]}
          value={message}
          onChangeText={onChangeText}
          placeholder="Ask me anything about your finances..."
          placeholderTextColor={colors.textLight}
          multiline
          maxLength={500}
          onContentSizeChange={handleContentSizeChange}
          editable={!isLoading && !disabled}
        />

        <Animated.View
          style={{
            transform: [
              { scale: scaleAnim },
              { scale: message.trim() || image ? pulseAnim : 1 }
            ],
          }}
        >
          <TouchableOpacity
            style={[
              styles.sendButton,
              (!message.trim() && !image) ? styles.micButton : styles.sendActiveButton,
            ]}
            onPress={onSend}
            onPressIn={handlePressIn}
            onPressOut={handlePressOut}
            disabled={isLoading || disabled || (!message.trim() && !image)}
          >
            {isLoading ? (
              <ActivityIndicator size="small" color="#FFFFFF" />
            ) : !message.trim() && !image ? (
              <Mic size={20} color={colors.primary} />
            ) : (
              <LinearGradient
                colors={[colors.primary, colors.aiAccent]}
                style={styles.sendGradient}
                start={{ x: 0, y: 0 }}
                end={{ x: 1, y: 1 }}
              >
                <Send size={18} color="#FFFFFF" />
              </LinearGradient>
            )}
          </TouchableOpacity>
        </Animated.View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    padding: 12,
    borderTopWidth: 1,
  },
  inputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    borderRadius: 24,
    paddingHorizontal: 8,
    paddingVertical: 4,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 3,
  },
  inputActions: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  imageButton: {
    padding: 8,
  },
  attachButton: {
    padding: 8,
  },
  input: {
    flex: 1,
    fontSize: 16,
    paddingHorizontal: 12,
    paddingVertical: 10,
    borderRadius: 20,
    marginHorizontal: 4,
  },
  inputWeb: {
    paddingTop: 8,
    paddingBottom: 8,
  } as any,
  sendButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginLeft: 4,
    marginRight: 4,
  },
  sendActiveButton: {
    backgroundColor: 'transparent',
  },
  micButton: {
    backgroundColor: 'rgba(0, 0, 0, 0.05)',
  },
  sendGradient: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  disabledButton: {
    opacity: 0.5,
  },
  imagePreviewContainer: {
    marginBottom: 12,
    borderRadius: 16,
    overflow: 'hidden',
    position: 'relative',
    alignSelf: 'flex-start',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.15,
    shadowRadius: 8,
    elevation: 4,
  },
  imagePreview: {
    width: 140,
    height: 140,
    borderRadius: 16,
  },
  clearImageButton: {
    position: 'absolute',
    top: 8,
    right: 8,
    width: 28,
    height: 28,
    borderRadius: 14,
    justifyContent: 'center',
    alignItems: 'center',
  },
});

export default ChatInput;