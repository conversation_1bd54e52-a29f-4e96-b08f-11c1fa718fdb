import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, FlatList, TouchableOpacity, ActivityIndicator, Modal } from 'react-native';
import { Stack, useRouter } from 'expo-router';
import { useTransactionStore } from '@/store/transactionStore';
import TransactionItem from '@/components/TransactionItem';
import EmptyState from '@/components/EmptyState';
import { useTheme } from '@/context/ThemeContext';
import { Plus, Filter, TrendingUp, Calendar } from 'lucide-react-native';
import { StatusBar } from 'expo-status-bar';
import { useToast } from '@/context/ToastContext';
import { useAnalytics } from '@/context/AnalyticsContext';
import DateRangePicker from '@/components/DateRangePicker';

export default function TransactionsScreen() {
  const { transactions, isLoading, deleteTransaction } = useTransactionStore();
  const [filter, setFilter] = useState<'all' | 'income' | 'expense'>('all');
  const [sortOrder, setSortOrder] = useState<'newest' | 'oldest'>('newest');
  const [showDatePicker, setShowDatePicker] = useState(false);
  const [dateRange, setDateRange] = useState({
    startDate: new Date(new Date().setMonth(new Date().getMonth() - 1)).toISOString().split('T')[0],
    endDate: new Date().toISOString().split('T')[0],
  });
  const [isDateFiltered, setIsDateFiltered] = useState(false);
  
  const router = useRouter();
  const { theme, colors: themeColors } = useTheme();
  const { showToast } = useToast();
  const { trackScreen, trackEvent } = useAnalytics();

  useEffect(() => {
    trackScreen('Transactions');
  }, []);

  const handleDeleteTransaction = async (id: string): Promise<void> => {
    try {
      await deleteTransaction(id);
      showToast({
        message: 'Transaction deleted successfully',
        type: 'success',
        duration: 2000
      });
      trackEvent('transaction_deleted');
    } catch (error) {
      console.error('Error deleting transaction:', error);
      showToast({
        message: 'Failed to delete transaction',
        type: 'error',
        duration: 3000
      });
    }
  };

  const handleDateRangeChange = (startDate: string, endDate: string) => {
    setDateRange({ startDate, endDate });
    setIsDateFiltered(true);
    setShowDatePicker(false);
  };

  const clearDateFilter = () => {
    setIsDateFiltered(false);
    setShowDatePicker(false);
  };

  const filteredTransactions = transactions.filter((transaction) => {
    // Filter by transaction type
    if (filter !== 'all' && transaction.type !== filter) {
      return false;
    }
    
    // Filter by date range if date filter is active
    if (isDateFiltered) {
      const transactionDate = new Date(transaction.date).getTime();
      const startDate = new Date(dateRange.startDate).getTime();
      const endDate = new Date(dateRange.endDate).setHours(23, 59, 59, 999); // End of the day
      
      if (transactionDate < startDate || transactionDate > endDate) {
        return false;
      }
    }
    
    return true;
  });

  const sortedTransactions = [...filteredTransactions].sort((a, b) => {
    const dateA = new Date(a.date).getTime();
    const dateB = new Date(b.date).getTime();
    return sortOrder === 'newest' ? dateB - dateA : dateA - dateB;
  });

  const renderItem = ({ item }: { item: any }) => (
    <TransactionItem
      transaction={item}
      onDelete={handleDeleteTransaction}
    />
  );

  return (
    <>
      <Stack.Screen
        options={{
          title: 'Transactions',
          headerStyle: { backgroundColor: themeColors.background },
          headerTintColor: themeColors.text,
          headerRight: () => (
            <TouchableOpacity
              style={styles.addButton}
              onPress={() => router.push('/add')}
            >
              <Plus size={24} color={themeColors.primary} />
            </TouchableOpacity>
          ),
        }}
      />

      <StatusBar style={theme === 'dark' ? 'light' : 'dark'} />

      <View style={[styles.container, { backgroundColor: themeColors.background }]}>
        <View style={styles.filterContainer}>
          <View style={styles.filterButtons}>
            <TouchableOpacity
              style={[
                styles.filterButton,
                filter === 'all' && { backgroundColor: themeColors.primary },
                { borderColor: themeColors.border }
              ]}
              onPress={() => setFilter('all')}
            >
              <Text
                style={[
                  styles.filterButtonText,
                  filter === 'all'
                    ? { color: themeColors.background }
                    : { color: themeColors.text }
                ]}
              >
                All
              </Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={[
                styles.filterButton,
                filter === 'income' && { backgroundColor: themeColors.income },
                { borderColor: themeColors.border }
              ]}
              onPress={() => setFilter('income')}
            >
              <Text
                style={[
                  styles.filterButtonText,
                  filter === 'income'
                    ? { color: themeColors.background }
                    : { color: themeColors.text }
                ]}
              >
                Income
              </Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={[
                styles.filterButton,
                filter === 'expense' && { backgroundColor: themeColors.expense },
                { borderColor: themeColors.border }
              ]}
              onPress={() => setFilter('expense')}
            >
              <Text
                style={[
                  styles.filterButtonText,
                  filter === 'expense'
                    ? { color: themeColors.background }
                    : { color: themeColors.text }
                ]}
              >
                Expenses
              </Text>
            </TouchableOpacity>
          </View>

          <View style={styles.rightFilters}>
            <TouchableOpacity
              style={[styles.dateButton, { borderColor: themeColors.border }]}
              onPress={() => setShowDatePicker(true)}
            >
              <Calendar size={16} color={themeColors.text} />
            </TouchableOpacity>
            
            <TouchableOpacity
              style={[styles.sortButton, { borderColor: themeColors.border }]}
              onPress={() => setSortOrder(sortOrder === 'newest' ? 'oldest' : 'newest')}
            >
              <Filter size={16} color={themeColors.text} />
              <Text style={[styles.sortButtonText, { color: themeColors.text }]}>
                {sortOrder === 'newest' ? 'Newest' : 'Oldest'}
              </Text>
            </TouchableOpacity>
          </View>
        </View>

        {isDateFiltered && (
          <View style={[styles.dateFilterBanner, { backgroundColor: themeColors.primary + '15' }]}>
            <Text style={[styles.dateFilterText, { color: themeColors.text }]}>
              Showing transactions from {new Date(dateRange.startDate).toLocaleDateString()} to {new Date(dateRange.endDate).toLocaleDateString()}
            </Text>
            <TouchableOpacity onPress={clearDateFilter}>
              <Text style={[styles.clearFilterText, { color: themeColors.primary }]}>Clear</Text>
            </TouchableOpacity>
          </View>
        )}

        {isLoading ? (
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" color={themeColors.primary} />
          </View>
        ) : sortedTransactions.length > 0 ? (
          <FlatList
            data={sortedTransactions}
            renderItem={renderItem}
            keyExtractor={(item) => item.id}
            contentContainerStyle={styles.listContent}
          />
        ) : (
          <EmptyState
            title="No transactions found"
            message={isDateFiltered ? "No transactions in the selected date range." : "Add your first transaction to start tracking your finances."}
            buttonText="Add Transaction"
            onPress={() => router.push('/add')}
            icon={<TrendingUp size={48} color={themeColors.primary} />}
          />
        )}
      </View>

      {/* Date Range Picker Modal */}
      <Modal
        visible={showDatePicker}
        transparent={true}
        animationType="fade"
        onRequestClose={() => setShowDatePicker(false)}
      >
        <DateRangePicker
          initialStartDate={dateRange.startDate}
          initialEndDate={dateRange.endDate}
          onSave={handleDateRangeChange}
          onCancel={() => setShowDatePicker(false)}
        />
      </Modal>
    </>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  addButton: {
    marginRight: 16,
  },
  filterContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
  },
  filterButtons: {
    flexDirection: 'row',
  },
  filterButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    marginRight: 8,
    borderWidth: 1,
  },
  filterButtonText: {
    fontSize: 14,
    fontWeight: '500',
  },
  rightFilters: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  dateButton: {
    padding: 8,
    borderRadius: 8,
    borderWidth: 1,
    marginRight: 8,
  },
  sortButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
    borderWidth: 1,
  },
  sortButtonText: {
    fontSize: 12,
    marginLeft: 4,
  },
  dateFilterBanner: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 8,
    marginBottom: 8,
  },
  dateFilterText: {
    fontSize: 12,
    flex: 1,
  },
  clearFilterText: {
    fontSize: 12,
    fontWeight: '600',
    marginLeft: 8,
  },
  listContent: {
    paddingHorizontal: 16,
    paddingBottom: 24,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
});