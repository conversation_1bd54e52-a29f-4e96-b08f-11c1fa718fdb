import React, { useState } from 'react';
import { 
  View, 
  Text, 
  StyleSheet, 
  TouchableOpacity, 
  ScrollView, 
  TextInput,
  Alert,
  Modal,
  KeyboardAvoidingView,
  Platform
} from 'react-native';
import { Stack, useRouter } from 'expo-router';
import { ArrowLeft, Edit2, Plus, X } from 'lucide-react-native';
import { useTheme } from '@/context/ThemeContext';
import { StatusBar } from 'expo-status-bar';
import { Category, getCategory, getAllCategories } from '@/constants/categories';

export default function CategoriesScreen() {
  const router = useRouter();
  const { theme, colors: themeColors } = useTheme();
  const [modalVisible, setModalVisible] = useState(false);
  const [editingCategory, setEditingCategory] = useState<string | null>(null);
  const [categoryName, setCategoryName] = useState('');
  const [categoryColor, setCategoryColor] = useState('#000000');
  
  // Get categories from the constants
  const categories = getAllCategories();
  
  // Fixed type issues by explicitly typing the filter callback
  const incomeCategories = categories.filter((cat: Category) => cat.type === 'income');
  const expenseCategories = categories.filter((cat: Category) => cat.type === 'expense');
  
  const handleAddCategory = () => {
    setEditingCategory(null);
    setCategoryName('');
    setCategoryColor('#3B82F6');
    setModalVisible(true);
  };
  
  const handleEditCategory = (categoryId: string) => {
    const category = getCategory(categoryId);
    setEditingCategory(categoryId);
    setCategoryName(category.name);
    setCategoryColor(category.color);
    setModalVisible(true);
  };
  
  const handleSaveCategory = () => {
    if (!categoryName.trim()) {
      Alert.alert('Error', 'Please enter a category name');
      return;
    }
    
    // In a real app, you would save the category to your storage
    // For this demo, we'll just close the modal
    setModalVisible(false);
    
    // Show a success message
    Alert.alert(
      'Success', 
      editingCategory 
        ? 'Category updated successfully' 
        : 'Category added successfully'
    );
  };
  
  const handleDeleteCategory = (categoryId: string) => {
    Alert.alert(
      'Delete Category',
      'Are you sure you want to delete this category? This will affect all transactions using this category.',
      [
        {
          text: 'Cancel',
          style: 'cancel',
        },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: () => {
            // In a real app, you would delete the category from your storage
            // For this demo, we'll just show an alert
            Alert.alert('Success', 'Category deleted successfully');
          },
        },
      ],
      { cancelable: true }
    );
  };
  
  const colorOptions = [
    '#EF4444', // Red
    '#F97316', // Orange
    '#F59E0B', // Amber
    '#10B981', // Emerald
    '#3B82F6', // Blue
    '#6366F1', // Indigo
    '#8B5CF6', // Violet
    '#EC4899', // Pink
  ];
  
  return (
    <>
      <Stack.Screen 
        options={{
          title: 'Categories',
          headerStyle: { backgroundColor: themeColors.background },
          headerTintColor: themeColors.text,
          headerShadowVisible: false,
          headerLeft: () => (
            <TouchableOpacity onPress={() => router.back()} style={styles.backButton}>
              <ArrowLeft size={24} color={themeColors.text} />
            </TouchableOpacity>
          ),
        }} 
      />
      
      <StatusBar style={theme === 'dark' ? 'light' : 'dark'} />
      
      <View style={[styles.container, { backgroundColor: themeColors.background }]}>
        <ScrollView style={styles.scrollView}>
          <View style={styles.content}>
            <Text style={[styles.sectionTitle, { color: themeColors.text }]}>
              Income Categories
            </Text>
            
            <View style={styles.categoriesList}>
              {incomeCategories.map((category: Category) => (
                <View 
                  key={category.id} 
                  style={[styles.categoryItem, { backgroundColor: themeColors.card }]}
                >
                  <View style={styles.categoryInfo}>
                    <View style={[styles.categoryDot, { backgroundColor: category.color }]} />
                    <Text style={[styles.categoryName, { color: themeColors.text }]}>
                      {category.name}
                    </Text>
                  </View>
                  
                  <TouchableOpacity 
                    style={styles.editButton}
                    onPress={() => handleEditCategory(category.id)}
                  >
                    <Edit2 size={18} color={themeColors.primary} />
                  </TouchableOpacity>
                </View>
              ))}
            </View>
            
            <Text style={[styles.sectionTitle, { color: themeColors.text, marginTop: 24 }]}>
              Expense Categories
            </Text>
            
            <View style={styles.categoriesList}>
              {expenseCategories.map((category: Category) => (
                <View 
                  key={category.id} 
                  style={[styles.categoryItem, { backgroundColor: themeColors.card }]}
                >
                  <View style={styles.categoryInfo}>
                    <View style={[styles.categoryDot, { backgroundColor: category.color }]} />
                    <Text style={[styles.categoryName, { color: themeColors.text }]}>
                      {category.name}
                    </Text>
                  </View>
                  
                  <TouchableOpacity 
                    style={styles.editButton}
                    onPress={() => handleEditCategory(category.id)}
                  >
                    <Edit2 size={18} color={themeColors.primary} />
                  </TouchableOpacity>
                </View>
              ))}
            </View>
          </View>
        </ScrollView>
        
        <TouchableOpacity 
          style={[styles.addButton, { backgroundColor: themeColors.primary }]}
          onPress={handleAddCategory}
        >
          <Plus size={24} color="#FFFFFF" />
        </TouchableOpacity>
        
        <Modal
          visible={modalVisible}
          transparent={true}
          animationType="slide"
          onRequestClose={() => setModalVisible(false)}
        >
          <KeyboardAvoidingView 
            style={styles.modalContainer} 
            behavior={Platform.OS === 'ios' ? 'padding' : undefined}
          >
            <View style={[styles.modalContent, { backgroundColor: themeColors.card }]}>
              <View style={styles.modalHeader}>
                <Text style={[styles.modalTitle, { color: themeColors.text }]}>
                  {editingCategory ? 'Edit Category' : 'Add Category'}
                </Text>
                
                <TouchableOpacity 
                  style={styles.closeButton}
                  onPress={() => setModalVisible(false)}
                >
                  <X size={24} color={themeColors.text} />
                </TouchableOpacity>
              </View>
              
              <View style={styles.formGroup}>
                <Text style={[styles.label, { color: themeColors.text }]}>Name</Text>
                <TextInput
                  style={[
                    styles.input, 
                    { 
                      backgroundColor: themeColors.background, 
                      color: themeColors.text,
                      borderColor: themeColors.border
                    }
                  ]}
                  value={categoryName}
                  onChangeText={setCategoryName}
                  placeholder="Category name"
                  placeholderTextColor={themeColors.textLight}
                />
              </View>
              
              <View style={styles.formGroup}>
                <Text style={[styles.label, { color: themeColors.text }]}>Color</Text>
                <View style={styles.colorOptions}>
                  {colorOptions.map(color => (
                    <TouchableOpacity
                      key={color}
                      style={[
                        styles.colorOption,
                        { backgroundColor: color },
                        categoryColor === color && styles.selectedColorOption,
                      ]}
                      onPress={() => setCategoryColor(color)}
                    />
                  ))}
                </View>
              </View>
              
              <View style={styles.formGroup}>
                <Text style={[styles.label, { color: themeColors.text }]}>Type</Text>
                <View style={styles.typeOptions}>
                  <TouchableOpacity 
                    style={[
                      styles.typeOption, 
                      { backgroundColor: themeColors.background },
                      styles.selectedTypeOption,
                      { borderColor: themeColors.primary }
                    ]}
                  >
                    <Text style={[styles.typeText, { color: themeColors.primary }]}>
                      {editingCategory && getCategory(editingCategory).type === 'income' ? 'Income' : 'Expense'}
                    </Text>
                  </TouchableOpacity>
                </View>
              </View>
              
              <View style={styles.modalActions}>
                {editingCategory && (
                  <TouchableOpacity 
                    style={[styles.deleteButton, { backgroundColor: themeColors.danger + '20' }]}
                    onPress={() => {
                      setModalVisible(false);
                      handleDeleteCategory(editingCategory);
                    }}
                  >
                    <Text style={[styles.deleteButtonText, { color: themeColors.danger }]}>
                      Delete
                    </Text>
                  </TouchableOpacity>
                )}
                
                <TouchableOpacity 
                  style={[styles.saveButton, { backgroundColor: themeColors.primary }]}
                  onPress={handleSaveCategory}
                >
                  <Text style={styles.saveButtonText}>
                    Save
                  </Text>
                </TouchableOpacity>
              </View>
            </View>
          </KeyboardAvoidingView>
        </Modal>
      </View>
    </>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  content: {
    padding: 16,
    paddingBottom: 80,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 12,
  },
  categoriesList: {
    gap: 8,
  },
  categoryItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 16,
    borderRadius: 12,
  },
  categoryInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  categoryDot: {
    width: 16,
    height: 16,
    borderRadius: 8,
    marginRight: 12,
  },
  categoryName: {
    fontSize: 16,
    fontWeight: '500',
  },
  editButton: {
    padding: 8,
  },
  addButton: {
    position: 'absolute',
    bottom: 24,
    right: 24,
    width: 56,
    height: 56,
    borderRadius: 28,
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: 'rgba(0, 0, 0, 0.2)',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.8,
    shadowRadius: 4,
    elevation: 5,
  },
  backButton: {
    padding: 8,
  },
  modalContainer: {
    flex: 1,
    justifyContent: 'flex-end',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  modalContent: {
    borderTopLeftRadius: 24,
    borderTopRightRadius: 24,
    padding: 24,
    paddingBottom: Platform.OS === 'ios' ? 40 : 24,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 24,
  },
  modalTitle: {
    fontSize: 20,
    fontWeight: '600',
  },
  closeButton: {
    padding: 4,
  },
  formGroup: {
    marginBottom: 20,
  },
  label: {
    fontSize: 16,
    fontWeight: '500',
    marginBottom: 8,
  },
  input: {
    borderWidth: 1,
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
  },
  colorOptions: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 12,
  },
  colorOption: {
    width: 36,
    height: 36,
    borderRadius: 18,
  },
  selectedColorOption: {
    borderWidth: 3,
    borderColor: '#FFFFFF',
    shadowColor: 'rgba(0, 0, 0, 0.3)',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.8,
    shadowRadius: 2,
    elevation: 3,
  },
  typeOptions: {
    flexDirection: 'row',
    gap: 12,
  },
  typeOption: {
    paddingVertical: 10,
    paddingHorizontal: 20,
    borderRadius: 8,
    borderWidth: 2,
  },
  selectedTypeOption: {
    borderWidth: 2,
  },
  typeText: {
    fontSize: 16,
    fontWeight: '500',
  },
  modalActions: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    marginTop: 24,
  },
  saveButton: {
    paddingVertical: 12,
    paddingHorizontal: 24,
    borderRadius: 8,
    alignItems: 'center',
    justifyContent: 'center',
  },
  saveButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
  },
  deleteButton: {
    paddingVertical: 12,
    paddingHorizontal: 24,
    borderRadius: 8,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 12,
  },
  deleteButtonText: {
    fontSize: 16,
    fontWeight: '600',
  },
});