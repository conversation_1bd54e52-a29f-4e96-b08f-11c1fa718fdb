import React, { useState } from 'react';
import { 
  View, 
  Text, 
  StyleSheet, 
  TouchableOpacity, 
  ScrollView, 
  TextInput,
  Alert
} from 'react-native';
import { Stack, useRouter } from 'expo-router';
import { getExpenseCategories } from '@/constants/categories';
import { useBudgetStore } from '@/store/budgetStore';
import { formatCurrency } from '@/utils/formatters';
import * as Haptics from 'expo-haptics';
import { Platform } from 'react-native';
import { Plus, Trash2, ArrowLeft } from 'lucide-react-native';
import { useTheme } from '@/context/ThemeContext';

export default function BudgetScreen() {
  const router = useRouter();
  const { budgets, setBudget, removeBudget } = useBudgetStore();
  const [editingCategoryId, setEditingCategoryId] = useState<string | null>(null);
  const [budgetAmount, setBudgetAmount] = useState('');
  const { colors: themeColors, theme } = useTheme();
  
  const expenseCategories = getExpenseCategories();
  
  const handleSetBudget = (categoryId: string) => {
    setEditingCategoryId(categoryId);
    setBudgetAmount(budgets[categoryId]?.toString() || '');
    
    if (Platform.OS !== 'web') {
      Haptics.selectionAsync();
    }
  };
  
  const handleSaveBudget = () => {
    if (!editingCategoryId) return;
    
    const amount = parseFloat(budgetAmount);
    if (isNaN(amount) || amount <= 0) {
      Alert.alert('Invalid Amount', 'Please enter a valid budget amount greater than zero.');
      return;
    }
    
    setBudget(editingCategoryId, amount);
    setEditingCategoryId(null);
    setBudgetAmount('');
    
    if (Platform.OS !== 'web') {
      Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
    }
  };
  
  const handleRemoveBudget = (categoryId: string) => {
    Alert.alert(
      'Remove Budget',
      'Are you sure you want to remove this budget?',
      [
        {
          text: 'Cancel',
          style: 'cancel',
        },
        {
          text: 'Remove',
          style: 'destructive',
          onPress: () => {
            removeBudget(categoryId);
            if (Platform.OS !== 'web') {
              Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
            }
          },
        },
      ]
    );
  };

  const goBack = () => {
    router.back();
  };
  
  return (
    <>
      <Stack.Screen options={{ 
        title: "Budget Settings",
        headerShown: true,
        headerStyle: { backgroundColor: themeColors.background },
        headerTintColor: themeColors.text,
        headerShadowVisible: false,
        headerLeft: () => (
          <TouchableOpacity onPress={goBack} style={styles.backButton}>
            <ArrowLeft size={24} color={themeColors.text} />
          </TouchableOpacity>
        ),
      }} />
      
      <View style={[styles.container, { backgroundColor: themeColors.background }]}>
        <ScrollView contentContainerStyle={styles.scrollContent}>
          <Text style={[styles.description, { color: themeColors.textLight }]}>
            Set monthly budgets for your expense categories to help track and manage your spending.
          </Text>
          
          <View style={styles.section}>
            <Text style={[styles.sectionTitle, { color: themeColors.text }]}>Category Budgets</Text>
            
            {expenseCategories.map(category => (
              <View key={category.id} style={[styles.categoryItem, { backgroundColor: themeColors.card }]}>
                <View style={styles.categoryInfo}>
                  <View 
                    style={[
                      styles.categoryDot, 
                      { backgroundColor: category.color }
                    ]} 
                  />
                  <Text style={[styles.categoryName, { color: themeColors.text }]}>{category.name}</Text>
                </View>
                
                {editingCategoryId === category.id ? (
                  <View style={styles.editBudgetContainer}>
                    <TextInput
                      style={[styles.budgetInput, { backgroundColor: themeColors.background, borderColor: themeColors.border, color: themeColors.text }]}
                      value={budgetAmount}
                      onChangeText={setBudgetAmount}
                      keyboardType="decimal-pad"
                      placeholder="Enter amount"
                      placeholderTextColor={themeColors.textLight}
                      autoFocus
                    />
                    <TouchableOpacity
                      style={[styles.saveButton, { backgroundColor: themeColors.primary }]}
                      onPress={handleSaveBudget}
                    >
                      <Text style={[styles.saveButtonText, { color: themeColors.background }]}>Save</Text>
                    </TouchableOpacity>
                  </View>
                ) : (
                  <View style={styles.budgetActions}>
                    {budgets[category.id] ? (
                      <>
                        <Text style={[styles.budgetAmount, { color: themeColors.text }]}>
                          {formatCurrency(budgets[category.id])}
                        </Text>
                        <View style={styles.actionButtons}>
                          <TouchableOpacity
                            style={[styles.editButton, { backgroundColor: themeColors.primary + '20' }]}
                            onPress={() => handleSetBudget(category.id)}
                          >
                            <Text style={[styles.editButtonText, { color: themeColors.primary }]}>Edit</Text>
                          </TouchableOpacity>
                          <TouchableOpacity
                            style={styles.removeButton}
                            onPress={() => handleRemoveBudget(category.id)}
                          >
                            <Trash2 size={16} color={themeColors.expense} />
                          </TouchableOpacity>
                        </View>
                      </>
                    ) : (
                      <TouchableOpacity
                        style={[styles.addButton, { borderColor: themeColors.primary }]}
                        onPress={() => handleSetBudget(category.id)}
                      >
                        <Plus size={16} color={themeColors.primary} />
                        <Text style={[styles.addButtonText, { color: themeColors.primary }]}>Set Budget</Text>
                      </TouchableOpacity>
                    )}
                  </View>
                )}
              </View>
            ))}
          </View>
          
          <View style={[styles.tipContainer, { backgroundColor: themeColors.card }]}>
            <Text style={[styles.tipTitle, { color: themeColors.text }]}>Budget Tips</Text>
            <Text style={[styles.tipText, { color: themeColors.textLight }]}>
              • The 50/30/20 rule suggests spending 50% on needs, 30% on wants, and 20% on savings.
            </Text>
            <Text style={[styles.tipText, { color: themeColors.textLight }]}>
              • Track your spending for a month before setting budgets to get a realistic baseline.
            </Text>
            <Text style={[styles.tipText, { color: themeColors.textLight }]}>
              • Review and adjust your budgets regularly as your financial situation changes.
            </Text>
          </View>
        </ScrollView>
      </View>
    </>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollContent: {
    padding: 16,
    paddingBottom: 32,
  },
  description: {
    fontSize: 16,
    marginBottom: 24,
  },
  section: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 12,
  },
  categoryItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    borderRadius: 12,
    padding: 16,
    marginBottom: 8,
  },
  categoryInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  categoryDot: {
    width: 16,
    height: 16,
    borderRadius: 8,
    marginRight: 12,
  },
  categoryName: {
    fontSize: 16,
  },
  budgetActions: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  budgetAmount: {
    fontSize: 16,
    fontWeight: '600',
    marginRight: 12,
  },
  actionButtons: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  editButton: {
    paddingVertical: 6,
    paddingHorizontal: 12,
    borderRadius: 8,
    marginRight: 8,
  },
  editButtonText: {
    fontSize: 14,
    fontWeight: '500',
  },
  removeButton: {
    padding: 6,
  },
  addButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 6,
    paddingHorizontal: 12,
    borderRadius: 8,
    borderWidth: 1,
  },
  addButtonText: {
    fontSize: 14,
    fontWeight: '500',
    marginLeft: 4,
  },
  editBudgetContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  budgetInput: {
    borderRadius: 8,
    paddingVertical: 8,
    paddingHorizontal: 12,
    fontSize: 16,
    borderWidth: 1,
    width: 120,
    marginRight: 8,
  },
  saveButton: {
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderRadius: 8,
  },
  saveButtonText: {
    fontSize: 14,
    fontWeight: '500',
  },
  tipContainer: {
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
  },
  tipTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 12,
  },
  tipText: {
    fontSize: 14,
    marginBottom: 8,
  },
  backButton: {
    padding: 8,
  },
});