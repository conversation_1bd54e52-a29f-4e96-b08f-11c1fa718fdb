import React, { useState } from 'react';
import { 
  View, 
  Text, 
  StyleSheet, 
  TouchableOpacity, 
  ScrollView, 
  Alert,
  ActivityIndicator,
  Platform
} from 'react-native';
import { useLocalSearchParams, useRouter, Stack } from 'expo-router';
import { useTransactionStore } from '@/store/transactionStore';
import { formatCurrency } from '@/utils/formatters';
import { getCategory } from '@/constants/categories';
import { ArrowLeft, Pencil, Trash2, Calendar, FileText, Receipt } from 'lucide-react-native';
import { Image } from 'expo-image';
import * as Haptics from 'expo-haptics';
import { useTheme } from '@/context/ThemeContext';
import { StatusBar } from 'expo-status-bar';
import { useToast } from '@/context/ToastContext';

export default function TransactionDetailScreen() {
  const { id } = useLocalSearchParams<{ id: string }>();
  const router = useRouter();
  const { getTransactionById, deleteTransaction, isLoading } = useTransactionStore();
  const [isDeleting, setIsDeleting] = useState(false);
  const { theme, colors: themeColors } = useTheme();
  const { showToast } = useToast();
  
  const transaction = getTransactionById(id);
  
  if (!transaction) {
    return (
      <>
        <Stack.Screen 
          options={{
            title: 'Transaction Details',
            headerStyle: { backgroundColor: themeColors.background },
            headerTintColor: themeColors.text,
            headerLeft: () => (
              <TouchableOpacity onPress={() => router.back()} style={styles.backButton}>
                <ArrowLeft size={24} color={themeColors.text} />
              </TouchableOpacity>
            ),
          }} 
        />
        <StatusBar style={theme === 'dark' ? 'light' : 'dark'} />
        <View style={[styles.container, { backgroundColor: themeColors.background }]}>
          <View style={[styles.notFoundContainer, { backgroundColor: themeColors.card }]}>
            <Text style={[styles.notFoundText, { color: themeColors.text }]}>
              Transaction not found
            </Text>
            <Text style={[styles.notFoundSubtext, { color: themeColors.textLight }]}>
              The transaction you're looking for doesn't exist or has been deleted.
            </Text>
            <TouchableOpacity 
              style={[styles.backToTransactionsButton, { backgroundColor: themeColors.primary }]}
              onPress={() => router.push('/(tabs)/transactions')}
            >
              <Text style={[styles.backToTransactionsText, { color: '#FFFFFF' }]}>
                Back to Transactions
              </Text>
            </TouchableOpacity>
          </View>
        </View>
      </>
    );
  }
  
  const handleEdit = () => {
    router.push(`/transaction/edit/${transaction.id}`);
  };
  
  const handleDelete = () => {
    if (Platform.OS !== 'web') {
      Haptics.notificationAsync(Haptics.NotificationFeedbackType.Warning);
    }
    
    Alert.alert(
      "Delete Transaction",
      "Are you sure you want to delete this transaction? This action cannot be undone.",
      [
        {
          text: "Cancel",
          style: "cancel"
        },
        {
          text: "Delete",
          style: "destructive",
          onPress: async () => {
            try {
              setIsDeleting(true);
              await deleteTransaction(transaction.id);
              
              if (Platform.OS !== 'web') {
                Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
              }
              
              showToast({
                message: "Transaction deleted successfully",
                type: "success",
                duration: 2000
              });
              
              router.replace('/(tabs)/transactions');
            } catch (error) {
              console.error('Error deleting transaction:', error);
              
              showToast({
                message: "Failed to delete transaction",
                type: "error",
                duration: 3000
              });
              
              setIsDeleting(false);
            }
          }
        }
      ]
    );
  };
  
  const category = getCategory(transaction.category);
  
  return (
    <>
      <Stack.Screen 
        options={{
          title: 'Transaction Details',
          headerStyle: { backgroundColor: themeColors.background },
          headerTintColor: themeColors.text,
          headerLeft: () => (
            <TouchableOpacity onPress={() => router.back()} style={styles.backButton}>
              <ArrowLeft size={24} color={themeColors.text} />
            </TouchableOpacity>
          ),
          headerRight: () => (
            <View style={styles.headerButtons}>
              <TouchableOpacity 
                onPress={handleEdit} 
                style={styles.headerButton}
                disabled={isLoading || isDeleting}
              >
                <Pencil size={24} color={themeColors.text} />
              </TouchableOpacity>
              <TouchableOpacity 
                onPress={handleDelete} 
                style={styles.headerButton}
                disabled={isLoading || isDeleting}
              >
                {isDeleting ? (
                  <ActivityIndicator size="small" color={themeColors.danger} />
                ) : (
                  <Trash2 size={24} color={themeColors.danger} />
                )}
              </TouchableOpacity>
            </View>
          ),
        }} 
      />
      
      <StatusBar style={theme === 'dark' ? 'light' : 'dark'} />
      
      <ScrollView style={[styles.container, { backgroundColor: themeColors.background }]}>
        <View style={styles.content}>
          <View style={[styles.amountContainer, { backgroundColor: themeColors.card }]}>
            <Text 
              style={[
                styles.amountLabel, 
                { color: themeColors.textLight }
              ]}
            >
              {transaction.type === 'expense' ? 'Expense' : 'Income'}
            </Text>
            <Text 
              style={[
                styles.amount, 
                { 
                  color: transaction.type === 'expense' 
                    ? themeColors.expense 
                    : themeColors.income 
                }
              ]}
            >
              {transaction.type === 'expense' ? '-' : '+'}{formatCurrency(transaction.amount)}
            </Text>
          </View>
          
          <View style={[styles.detailsContainer, { backgroundColor: themeColors.card }]}>
            <View style={styles.detailRow}>
              <Text style={[styles.detailLabel, { color: themeColors.textLight }]}>Description</Text>
              <Text style={[styles.detailValue, { color: themeColors.text }]}>
                {transaction.description}
              </Text>
            </View>
            
            <View style={[styles.divider, { backgroundColor: themeColors.border }]} />
            
            <View style={styles.detailRow}>
              <Text style={[styles.detailLabel, { color: themeColors.textLight }]}>Category</Text>
              <View style={styles.categoryContainer}>
                <View 
                  style={[
                    styles.categoryDot, 
                    { backgroundColor: category.color }
                  ]} 
                />
                <Text style={[styles.detailValue, { color: themeColors.text }]}>
                  {category.name}
                </Text>
              </View>
            </View>
            
            <View style={[styles.divider, { backgroundColor: themeColors.border }]} />
            
            <View style={styles.detailRow}>
              <Text style={[styles.detailLabel, { color: themeColors.textLight }]}>Date</Text>
              <View style={styles.dateContainer}>
                <Calendar size={16} color={themeColors.primary} style={styles.dateIcon} />
                <Text style={[styles.detailValue, { color: themeColors.text }]}>
                  {new Date(transaction.date).toLocaleDateString()}
                </Text>
              </View>
            </View>
            
            {transaction.notes && (
              <>
                <View style={[styles.divider, { backgroundColor: themeColors.border }]} />
                
                <View style={styles.notesRow}>
                  <View style={styles.notesLabelContainer}>
                    <FileText size={16} color={themeColors.textLight} style={styles.notesIcon} />
                    <Text style={[styles.detailLabel, { color: themeColors.textLight }]}>Notes</Text>
                  </View>
                  <Text style={[styles.notesText, { color: themeColors.text }]}>
                    {transaction.notes}
                  </Text>
                </View>
              </>
            )}
          </View>
          
          {transaction.receiptUrl && (
            <View style={[styles.receiptContainer, { backgroundColor: themeColors.card }]}>
              <View style={styles.receiptHeader}>
                <Receipt size={20} color={themeColors.primary} />
                <Text style={[styles.receiptTitle, { color: themeColors.text }]}>Receipt</Text>
              </View>
              
              <Image
                source={{ uri: transaction.receiptUrl }}
                style={styles.receiptImage}
                contentFit="cover"
              />
            </View>
          )}
          
          <View style={[styles.metadataContainer, { backgroundColor: themeColors.card }]}>
            <Text style={[styles.metadataLabel, { color: themeColors.textLight }]}>
              Created: {new Date(transaction.createdAt).toLocaleString()}
            </Text>
            <Text style={[styles.metadataLabel, { color: themeColors.textLight }]}>
              Last Updated: {new Date(transaction.updatedAt).toLocaleString()}
            </Text>
          </View>
        </View>
      </ScrollView>
    </>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  content: {
    padding: 16,
  },
  backButton: {
    padding: 8,
  },
  headerButtons: {
    flexDirection: 'row',
  },
  headerButton: {
    padding: 8,
    marginLeft: 8,
  },
  amountContainer: {
    borderRadius: 16,
    padding: 20,
    alignItems: 'center',
    marginBottom: 16,
  },
  amountLabel: {
    fontSize: 16,
    marginBottom: 8,
  },
  amount: {
    fontSize: 32,
    fontWeight: '700',
  },
  detailsContainer: {
    borderRadius: 16,
    padding: 16,
    marginBottom: 16,
  },
  detailRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 12,
  },
  detailLabel: {
    fontSize: 16,
  },
  detailValue: {
    fontSize: 16,
    fontWeight: '500',
  },
  divider: {
    height: 1,
    width: '100%',
  },
  categoryContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  categoryDot: {
    width: 12,
    height: 12,
    borderRadius: 6,
    marginRight: 8,
  },
  dateContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  dateIcon: {
    marginRight: 8,
  },
  notesRow: {
    paddingVertical: 12,
  },
  notesLabelContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  notesIcon: {
    marginRight: 8,
  },
  notesText: {
    fontSize: 16,
    lineHeight: 24,
  },
  receiptContainer: {
    borderRadius: 16,
    padding: 16,
    marginBottom: 16,
  },
  receiptHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  receiptTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginLeft: 8,
  },
  receiptImage: {
    width: '100%',
    height: 300,
    borderRadius: 8,
  },
  metadataContainer: {
    borderRadius: 16,
    padding: 16,
    marginBottom: 32,
  },
  metadataLabel: {
    fontSize: 12,
    marginBottom: 4,
  },
  notFoundContainer: {
    margin: 16,
    padding: 24,
    borderRadius: 16,
    alignItems: 'center',
    justifyContent: 'center',
  },
  notFoundText: {
    fontSize: 20,
    fontWeight: '600',
    marginBottom: 8,
    textAlign: 'center',
  },
  notFoundSubtext: {
    fontSize: 16,
    textAlign: 'center',
    marginBottom: 24,
  },
  backToTransactionsButton: {
    paddingVertical: 12,
    paddingHorizontal: 20,
    borderRadius: 8,
  },
  backToTransactionsText: {
    fontSize: 16,
    fontWeight: '500',
  },
});