import AsyncStorage from '@react-native-async-storage/async-storage';
import * as SecureStore from 'expo-secure-store';
import { Platform } from 'react-native';
import { encrypt, decrypt } from './encryption';

/**
 * A unified storage utility that handles both AsyncStorage and SecureStore
 * with proper error handling and encryption for sensitive data.
 */
class StorageUtility {
  /**
   * Store a value in storage
   * @param key The key to store the value under
   * @param value The value to store
   * @param secure Whether to use secure storage (SecureStore)
   */
  async setItem(key: string, value: string, secure: boolean = false): Promise<void> {
    try {
      if (Platform.OS === 'web' || !secure) {
        await AsyncStorage.setItem(key, value);
      } else {
        // Encrypt data for secure storage
        const encryptedValue = encrypt(value);
        await SecureStore.setItemAsync(key, encryptedValue);
      }
    } catch (error) {
      console.error(`Error storing data for key ${key}:`, error);
      throw new Error(`Failed to store data: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * Retrieve a value from storage
   * @param key The key to retrieve
   * @param secure Whether to use secure storage (SecureStore)
   * @returns The stored value, or null if not found
   */
  async getItem(key: string, secure: boolean = false): Promise<string | null> {
    try {
      if (Platform.OS === 'web' || !secure) {
        return await AsyncStorage.getItem(key);
      } else {
        const encryptedValue = await SecureStore.getItemAsync(key);
        if (!encryptedValue) return null;

        // Decrypt data from secure storage
        return decrypt(encryptedValue);
      }
    } catch (error) {
      console.error(`Error retrieving data for key ${key}:`, error);
      throw new Error(`Failed to retrieve data: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * Remove a value from storage
   * @param key The key to remove
   * @param secure Whether to use secure storage (SecureStore)
   */
  async removeItem(key: string, secure: boolean = false): Promise<void> {
    try {
      if (Platform.OS === 'web' || !secure) {
        await AsyncStorage.removeItem(key);
      } else {
        await SecureStore.deleteItemAsync(key);
      }
    } catch (error) {
      console.error(`Error removing data for key ${key}:`, error);
      throw new Error(`Failed to remove data: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * Clear all values from storage
   * Note: This only clears AsyncStorage, not SecureStore
   */
  async clearAll(): Promise<void> {
    try {
      await AsyncStorage.clear();
    } catch (error) {
      console.error('Error clearing storage:', error);
      throw new Error(`Failed to clear storage: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * Get all keys from storage
   * Note: This only gets keys from AsyncStorage, not SecureStore
   */
  async getAllKeys(): Promise<string[]> {
    try {
      return [...await AsyncStorage.getAllKeys()];
    } catch (error) {
      console.error('Error getting all keys:', error);
      throw new Error(`Failed to get all keys: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * Check if storage is available
   * @returns True if storage is available
   */
  async isAvailable(): Promise<boolean> {
    try {
      const testKey = '__storage_test__';
      await this.setItem(testKey, 'test');
      await this.removeItem(testKey);
      return true;
    } catch (error) {
      console.error('Storage is not available:', error);
      return false;
    }
  }

  /**
   * Get storage usage information
   * Note: This is an estimate and may not be accurate
   * @returns Storage usage information
   */
  async getUsageInfo(): Promise<{ keys: number; estimatedSize: string }> {
    try {
      const keys = await this.getAllKeys();
      let totalSize = 0;

      for (const key of keys) {
        const value = await AsyncStorage.getItem(key);
        if (value) {
          totalSize += key.length + value.length;
        }
      }

      // Convert to KB
      const sizeInKB = (totalSize / 1024).toFixed(2);

      return {
        keys: keys.length,
        estimatedSize: `${sizeInKB} KB`,
      };
    } catch (error) {
      console.error('Error getting storage usage info:', error);
      return {
        keys: 0,
        estimatedSize: '0 KB',
      };
    }
  }
}

export const storage = new StorageUtility();