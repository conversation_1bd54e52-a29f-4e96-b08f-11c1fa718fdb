import React, { createContext, useContext, useState, useRef, useEffect } from 'react';
import { View, Text, StyleSheet, Animated, TouchableOpacity, Platform } from 'react-native';
import { useTheme } from './ThemeContext';
import { X } from 'lucide-react-native';

type ToastType = 'success' | 'error' | 'info' | 'warning';

interface ToastData {
  message: string;
  type: ToastType;
  duration?: number;
}

interface ToastContextType {
  showToast: (data: ToastData | string) => void;
  hideToast: () => void;
}

const ToastContext = createContext<ToastContextType>({
  showToast: () => {},
  hideToast: () => {},
});

export const useToast = () => useContext(ToastContext);

export const ToastProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [visible, setVisible] = useState(false);
  const [message, setMessage] = useState('');
  const [toastType, setToastType] = useState<ToastType>('info');
  const [duration, setDuration] = useState(3000);
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const slideAnim = useRef(new Animated.Value(-100)).current;
  const timeoutRef = useRef<NodeJS.Timeout | null>(null);
  const { colors: themeColors } = useTheme();
  
  const showToast = (data: ToastData | string) => {
    // Clear any existing timeout
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }
    
    // Handle string or object input
    if (typeof data === 'string') {
      setMessage(data);
      setToastType('info');
      setDuration(3000);
    } else {
      setMessage(data.message);
      setToastType(data.type || 'info');
      setDuration(data.duration || 3000);
    }
    
    setVisible(true);
    
    // Animate in
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 300,
        useNativeDriver: true,
      }),
      Animated.timing(slideAnim, {
        toValue: 0,
        duration: 300,
        useNativeDriver: true,
      }),
    ]).start();
    
    // Set timeout to hide
    timeoutRef.current = setTimeout(() => {
      hideToast();
    }, duration);
  };
  
  const hideToast = () => {
    // Animate out
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 0,
        duration: 300,
        useNativeDriver: true,
      }),
      Animated.timing(slideAnim, {
        toValue: -100,
        duration: 300,
        useNativeDriver: true,
      }),
    ]).start(() => {
      setVisible(false);
    });
    
    // Clear timeout
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
      timeoutRef.current = null;
    }
  };
  
  // Clean up timeout on unmount
  useEffect(() => {
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, []);
  
  // Get toast background color based on type
  const getToastBackgroundColor = () => {
    switch (toastType) {
      case 'success':
        return themeColors.income + '90';
      case 'error':
        return themeColors.expense + '90';
      case 'warning':
        return themeColors.warning + '90';
      case 'info':
      default:
        return themeColors.primary + '90';
    }
  };
  
  return (
    <ToastContext.Provider value={{ showToast, hideToast }}>
      {children}
      
      {visible && (
        <Animated.View 
          style={[
            styles.toastContainer, 
            { 
              backgroundColor: getToastBackgroundColor(),
              opacity: fadeAnim,
              transform: [{ translateY: slideAnim }],
              ...Platform.select({
                ios: {
                  shadowColor: 'rgba(0, 0, 0, 0.3)',
                  shadowOffset: { width: 0, height: 2 },
                  shadowOpacity: 0.3,
                  shadowRadius: 4,
                },
                android: {
                  elevation: 6,
                },
              }),
            }
          ]}
        >
          <Text style={[styles.toastMessage, { color: themeColors.background }]} numberOfLines={2}>
            {message}
          </Text>
          
          <TouchableOpacity onPress={hideToast} style={styles.closeButton}>
            <X size={16} color={themeColors.background} />
          </TouchableOpacity>
        </Animated.View>
      )}
    </ToastContext.Provider>
  );
};

const styles = StyleSheet.create({
  toastContainer: {
    position: 'absolute',
    top: Platform.OS === 'ios' ? 50 : 20,
    left: 16,
    right: 16,
    borderRadius: 12,
    padding: 16,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    zIndex: 9999,
  },
  toastMessage: {
    flex: 1,
    fontSize: 14,
    fontWeight: '500',
    marginRight: 8,
  },
  closeButton: {
    padding: 4,
  },
});