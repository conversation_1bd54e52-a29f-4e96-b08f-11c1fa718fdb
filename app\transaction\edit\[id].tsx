import React, { useState, useEffect } from 'react';
import { 
  View, 
  Text, 
  StyleSheet, 
  TextInput, 
  TouchableOpacity, 
  ScrollView, 
  KeyboardAvoidingView, 
  Platform,
  ActivityIndicator,
  Alert
} from 'react-native';
import { useLocalSearchPara<PERSON>, useRouter, Stack } from 'expo-router';
import { useTransactionStore } from '@/store/transactionStore';
import { getCategory, getAllCategories } from '@/constants/categories';
import { formatCurrency } from '@/utils/formatters';
import { ArrowLeft, Save, Calendar } from 'lucide-react-native';
import CategoryPicker from '@/components/CategoryPicker';
import TransactionTypeSwitch from '@/components/TransactionTypeSwitch';
import { useTheme } from '@/context/ThemeContext';
import { StatusBar } from 'expo-status-bar';
import { useToast } from '@/context/ToastContext';

// Define types for date picker events
interface DateTimePickerEvent {
  type: string;
  nativeEvent: {
    timestamp?: number;
  };
}

// DateTimePicker component for different platforms
const DatePickerComponent = ({ date, onChange }: { date: Date, onChange: (event: DateTimePickerEvent | null, selectedDate?: Date) => void }) => {
  const [showPicker, setShowPicker] = useState(false);
  const { colors: themeColors } = useTheme();

  // Only import DateTimePicker on native platforms
  if (Platform.OS !== 'web') {
    // Dynamic import to avoid the module not found error on web
    try {
      // We use require here to avoid issues with web platform
      const DateTimePicker = require('@react-native-community/datetimepicker').default;
      
      return (
        <>
          <TouchableOpacity
            style={[styles.dateButton, { borderColor: themeColors.border }]}
            onPress={() => setShowPicker(true)}
          >
            <View style={styles.dateButtonContent}>
              <Calendar size={18} color={themeColors.primary} style={styles.dateIcon} />
              <Text style={[styles.dateText, { color: themeColors.text }]}>
                {date.toLocaleDateString()}
              </Text>
            </View>
          </TouchableOpacity>
          
          {showPicker && (
            <DateTimePicker
              value={date}
              mode="date"
              display="default"
              onChange={(event: DateTimePickerEvent, selectedDate?: Date) => {
                setShowPicker(false);
                onChange(event, selectedDate);
              }}
              maximumDate={new Date()}
            />
          )}
        </>
      );
    } catch (error) {
      console.log('DateTimePicker not available:', error);
      // Fallback to web version if module is not available
    }
  }
  
  // Web fallback
  return (
    <TouchableOpacity
      style={[styles.dateButton, { borderColor: themeColors.border }]}
      onPress={() => {
        // No action needed, the input below handles date picking on web
      }}
    >
      <View style={styles.dateButtonContent}>
        <Calendar size={18} color={themeColors.primary} style={styles.dateIcon} />
        <TextInput
          style={[styles.dateInput, { color: themeColors.text }]}
          value={date.toISOString().split('T')[0]}
          onChangeText={(text) => {
            const newDate = new Date(text);
            if (!isNaN(newDate.getTime())) {
              onChange(null, newDate);
            }
          }}
          placeholder="YYYY-MM-DD"
          placeholderTextColor={themeColors.textLight}
        />
      </View>
    </TouchableOpacity>
  );
};

export default function EditTransactionScreen() {
  const { id } = useLocalSearchParams<{ id: string }>();
  const router = useRouter();
  const { getTransactionById, updateTransaction, isLoading } = useTransactionStore();
  const { theme, colors: themeColors } = useTheme();
  const { showToast } = useToast();
  
  const transaction = getTransactionById(id);
  
  const [amount, setAmount] = useState('');
  const [description, setDescription] = useState('');
  const [category, setCategory] = useState('');
  const [date, setDate] = useState(new Date());
  const [type, setType] = useState<'income' | 'expense'>('expense');
  
  useEffect(() => {
    if (transaction) {
      setAmount(transaction.amount.toString());
      setDescription(transaction.description);
      setCategory(transaction.category);
      setDate(new Date(transaction.date));
      setType(transaction.type);
    }
  }, [transaction]);
  
  if (!transaction) {
    return (
      <>
        <Stack.Screen 
          options={{
            title: 'Edit Transaction',
            headerStyle: { backgroundColor: themeColors.background },
            headerTintColor: themeColors.text,
            headerLeft: () => (
              <TouchableOpacity onPress={() => router.back()} style={styles.backButton}>
                <ArrowLeft size={24} color={themeColors.text} />
              </TouchableOpacity>
            ),
          }} 
        />
        <StatusBar style={theme === 'dark' ? 'light' : 'dark'} />
        <View style={[styles.container, { backgroundColor: themeColors.background }]}>
          <View style={[styles.notFoundContainer, { backgroundColor: themeColors.card }]}>
            <Text style={[styles.notFoundText, { color: themeColors.text }]}>
              Transaction not found
            </Text>
            <Text style={[styles.notFoundSubtext, { color: themeColors.textLight }]}>
              The transaction you're trying to edit doesn't exist or has been deleted.
            </Text>
            <TouchableOpacity 
              style={[styles.backToTransactionsButton, { backgroundColor: themeColors.primary }]}
              onPress={() => router.push('/(tabs)/transactions')}
            >
              <Text style={[styles.backToTransactionsText, { color: '#FFFFFF' }]}>
                Back to Transactions
              </Text>
            </TouchableOpacity>
          </View>
        </View>
      </>
    );
  }
  
  const handleSave = async () => {
    if (!amount || parseFloat(amount) <= 0) {
      showToast({
        message: "Please enter a valid amount",
        type: "error",
        duration: 3000
      });
      return;
    }
    
    if (!description.trim()) {
      showToast({
        message: "Please enter a description",
        type: "error",
        duration: 3000
      });
      return;
    }
    
    if (!category) {
      showToast({
        message: "Please select a category",
        type: "error",
        duration: 3000
      });
      return;
    }
    
    try {
      await updateTransaction(transaction.id, {
        amount: parseFloat(amount),
        description: description.trim(),
        category,
        date: date.toISOString().split('T')[0],
        type,
      });
      
      showToast({
        message: "Transaction updated successfully",
        type: "success",
        duration: 2000
      });
      
      router.back();
    } catch (error) {
      console.error('Error updating transaction:', error);
      showToast({
        message: "Failed to update transaction",
        type: "error",
        duration: 3000
      });
    }
  };
  
  const handleDateChange = (event: DateTimePickerEvent | null, selectedDate?: Date) => {
    if (selectedDate) {
      setDate(selectedDate);
    }
  };
  
  return (
    <>
      <Stack.Screen 
        options={{
          title: 'Edit Transaction',
          headerStyle: { backgroundColor: themeColors.background },
          headerTintColor: themeColors.text,
          headerLeft: () => (
            <TouchableOpacity onPress={() => router.back()} style={styles.backButton}>
              <ArrowLeft size={24} color={themeColors.text} />
            </TouchableOpacity>
          ),
          headerRight: () => (
            <TouchableOpacity 
              onPress={handleSave} 
              style={styles.saveButton}
              disabled={isLoading}
            >
              {isLoading ? (
                <ActivityIndicator size="small" color={themeColors.text} />
              ) : (
                <Save size={24} color={themeColors.text} />
              )}
            </TouchableOpacity>
          ),
        }} 
      />
      
      <StatusBar style={theme === 'dark' ? 'light' : 'dark'} />
      
      <KeyboardAvoidingView 
        style={{ flex: 1 }} 
        behavior={Platform.OS === 'ios' ? 'padding' : undefined}
        keyboardVerticalOffset={Platform.OS === 'ios' ? 90 : 0}
      >
        <ScrollView style={[styles.container, { backgroundColor: themeColors.background }]}>
          <View style={styles.content}>
            <TransactionTypeSwitch 
              value={type} 
              onChange={setType} 
            />
            
            <View style={[styles.inputContainer, { backgroundColor: themeColors.card }]}>
              <Text style={[styles.label, { color: themeColors.textLight }]}>Amount</Text>
              <TextInput
                style={[
                  styles.amountInput, 
                  { 
                    color: themeColors.text,
                    borderColor: themeColors.border,
                  }
                ]}
                value={amount}
                onChangeText={setAmount}
                keyboardType="numeric"
                placeholder="0.00"
                placeholderTextColor={themeColors.textLight}
              />
            </View>
            
            <View style={[styles.inputContainer, { backgroundColor: themeColors.card }]}>
              <Text style={[styles.label, { color: themeColors.textLight }]}>Description</Text>
              <TextInput
                style={[
                  styles.descriptionInput, 
                  { 
                    color: themeColors.text,
                    borderColor: themeColors.border,
                  }
                ]}
                value={description}
                onChangeText={setDescription}
                placeholder="What was this for?"
                placeholderTextColor={themeColors.textLight}
                multiline
              />
            </View>
            
            <View style={[styles.inputContainer, { backgroundColor: themeColors.card }]}>
              <Text style={[styles.label, { color: themeColors.textLight }]}>Category</Text>
              <CategoryPicker
                selectedCategory={category}
                onSelectCategory={setCategory}
                transactionType={type}
              />
            </View>
            
            <View style={[styles.inputContainer, { backgroundColor: themeColors.card }]}>
              <Text style={[styles.label, { color: themeColors.textLight }]}>Date</Text>
              <DatePickerComponent date={date} onChange={handleDateChange} />
            </View>
            
            <TouchableOpacity
              style={[
                styles.saveButtonLarge, 
                { backgroundColor: themeColors.primary }
              ]}
              onPress={handleSave}
              disabled={isLoading}
            >
              {isLoading ? (
                <ActivityIndicator size="small" color="#FFFFFF" />
              ) : (
                <>
                  <Save size={20} color="#FFFFFF" />
                  <Text style={styles.saveButtonText}>Save Changes</Text>
                </>
              )}
            </TouchableOpacity>
          </View>
        </ScrollView>
      </KeyboardAvoidingView>
    </>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  content: {
    padding: 16,
  },
  backButton: {
    padding: 8,
  },
  saveButton: {
    padding: 8,
  },
  inputContainer: {
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
  },
  label: {
    fontSize: 14,
    marginBottom: 8,
  },
  amountInput: {
    fontSize: 24,
    fontWeight: '600',
    borderBottomWidth: 1,
    paddingVertical: 8,
  },
  descriptionInput: {
    fontSize: 16,
    borderBottomWidth: 1,
    paddingVertical: 8,
    minHeight: 60,
  },
  dateButton: {
    borderBottomWidth: 1,
    paddingVertical: 12,
  },
  dateButtonContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  dateIcon: {
    marginRight: 8,
  },
  dateText: {
    fontSize: 16,
  },
  dateInput: {
    fontSize: 16,
    flex: 1,
    padding: 0,
  },
  saveButtonLarge: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 16,
    borderRadius: 12,
    marginTop: 16,
    marginBottom: 32,
  },
  saveButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
    marginLeft: 8,
  },
  notFoundContainer: {
    margin: 16,
    padding: 24,
    borderRadius: 16,
    alignItems: 'center',
    justifyContent: 'center',
  },
  notFoundText: {
    fontSize: 20,
    fontWeight: '600',
    marginBottom: 8,
    textAlign: 'center',
  },
  notFoundSubtext: {
    fontSize: 16,
    textAlign: 'center',
    marginBottom: 24,
  },
  backToTransactionsButton: {
    paddingVertical: 12,
    paddingHorizontal: 20,
    borderRadius: 8,
  },
  backToTransactionsText: {
    fontSize: 16,
    fontWeight: '500',
  },
});