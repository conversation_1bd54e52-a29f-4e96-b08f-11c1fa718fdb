import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity, ScrollView, Switch, Image } from 'react-native';
import { useRouter } from 'expo-router';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useTheme } from '@/context/ThemeContext';
import { useSettingsStore } from '@/store/settingsStore';
import { useBudget } from '@/store/budgetStore';
import { useAuth } from '@/context/AuthContext';
import { ChevronRight, Moon, Sun, LogOut, User, Shield, Bell, HelpCircle, CreditCard } from 'lucide-react-native';

export default function SettingsScreen() {
  const router = useRouter();
  const { colors, isDark, toggleTheme } = useTheme();
  const { currencyCode: currency } = useSettingsStore();
  const { monthlyBudget } = useBudget();
  const { user, signOut, isAuthenticated } = useAuth();

  const handleSignOut = async () => {
    await signOut();
    router.replace('/login');
  };

  const navigateTo = (path: string) => {
    router.push(path);
  };

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]} edges={['bottom']}>
      <ScrollView showsVerticalScrollIndicator={false}>
        {/* User Profile Section */}
        {isAuthenticated && user ? (
          <View style={[styles.profileSection, { backgroundColor: colors.card }]}>
            <View style={styles.profileInfo}>
              {user.photoUrl ? (
                <Image source={{ uri: user.photoUrl }} style={styles.profileImage} />
              ) : (
                <View style={[styles.profileImagePlaceholder, { backgroundColor: colors.primary + '30' }]}>
                  <User size={24} color={colors.primary} />
                </View>
              )}
              <View style={styles.profileText}>
                <Text style={[styles.profileName, { color: colors.text }]}>{user.name || 'User'}</Text>
                <Text style={[styles.profileEmail, { color: colors.textSecondary }]}>{user.email}</Text>
              </View>
            </View>
            <TouchableOpacity onPress={() => navigateTo('/settings/profile')}>
              <ChevronRight size={20} color={colors.textSecondary} />
            </TouchableOpacity>
          </View>
        ) : (
          <TouchableOpacity 
            style={[styles.profileSection, { backgroundColor: colors.card }]}
            onPress={() => router.push('/login')}
          >
            <View style={styles.profileInfo}>
              <View style={[styles.profileImagePlaceholder, { backgroundColor: colors.primary + '30' }]}>
                <User size={24} color={colors.primary} />
              </View>
              <Text style={[styles.loginText, { color: colors.primary }]}>Sign in</Text>
            </View>
            <ChevronRight size={20} color={colors.textSecondary} />
          </TouchableOpacity>
        )}

        {/* Preferences Section */}
        <View style={styles.section}>
          <Text style={[styles.sectionTitle, { color: colors.textSecondary }]}>Preferences</Text>
          
          <View style={[styles.settingsGroup, { backgroundColor: colors.card }]}>
            <TouchableOpacity 
              style={styles.settingsItem}
              onPress={() => navigateTo('/settings/categories')}
            >
              <Text style={[styles.settingsLabel, { color: colors.text }]}>Categories</Text>
              <View style={styles.settingsValue}>
                <Text style={[styles.settingsValueText, { color: colors.textSecondary }]}>Manage</Text>
                <ChevronRight size={20} color={colors.textSecondary} />
              </View>
            </TouchableOpacity>
            
            <View style={[styles.separator, { backgroundColor: colors.border }]} />
            
            <TouchableOpacity 
              style={styles.settingsItem}
              onPress={() => navigateTo('/settings/currency')}
            >
              <Text style={[styles.settingsLabel, { color: colors.text }]}>Currency</Text>
              <View style={styles.settingsValue}>
                <Text style={[styles.settingsValueText, { color: colors.textSecondary }]}>{currency}</Text>
                <ChevronRight size={20} color={colors.textSecondary} />
              </View>
            </TouchableOpacity>
            
            <View style={[styles.separator, { backgroundColor: colors.border }]} />
            
            <TouchableOpacity 
              style={styles.settingsItem}
              onPress={() => navigateTo('/settings/budget')}
            >
              <Text style={[styles.settingsLabel, { color: colors.text }]}>Budget</Text>
              <View style={styles.settingsValue}>
                <Text style={[styles.settingsValueText, { color: colors.textSecondary }]}>
                  {currency} {monthlyBudget.toFixed(2)}
                </Text>
                <ChevronRight size={20} color={colors.textSecondary} />
              </View>
            </TouchableOpacity>
            
            <View style={[styles.separator, { backgroundColor: colors.border }]} />
            
            <View style={styles.settingsItem}>
              <Text style={[styles.settingsLabel, { color: colors.text }]}>Dark Mode</Text>
              <Switch
                value={isDark}
                onValueChange={toggleTheme}
                trackColor={{ false: colors.border, true: colors.primary + '70' }}
                thumbColor={isDark ? colors.primary : colors.card}
                ios_backgroundColor={colors.border}
              />
            </View>
          </View>
        </View>

        {/* App Section */}
        <View style={styles.section}>
          <Text style={[styles.sectionTitle, { color: colors.textSecondary }]}>App</Text>
          
          <View style={[styles.settingsGroup, { backgroundColor: colors.card }]}>
            <TouchableOpacity style={styles.settingsItem}>
              <View style={styles.settingsLabelWithIcon}>
                <Bell size={18} color={colors.text} style={styles.settingsIcon} />
                <Text style={[styles.settingsLabel, { color: colors.text }]}>Notifications</Text>
              </View>
              <ChevronRight size={20} color={colors.textSecondary} />
            </TouchableOpacity>
            
            <View style={[styles.separator, { backgroundColor: colors.border }]} />
            
            <TouchableOpacity style={styles.settingsItem}>
              <View style={styles.settingsLabelWithIcon}>
                <Shield size={18} color={colors.text} style={styles.settingsIcon} />
                <Text style={[styles.settingsLabel, { color: colors.text }]}>Privacy</Text>
              </View>
              <ChevronRight size={20} color={colors.textSecondary} />
            </TouchableOpacity>
            
            <View style={[styles.separator, { backgroundColor: colors.border }]} />
            
            <TouchableOpacity style={styles.settingsItem}>
              <View style={styles.settingsLabelWithIcon}>
                <HelpCircle size={18} color={colors.text} style={styles.settingsIcon} />
                <Text style={[styles.settingsLabel, { color: colors.text }]}>Help & Support</Text>
              </View>
              <ChevronRight size={20} color={colors.textSecondary} />
            </TouchableOpacity>
            
            <View style={[styles.separator, { backgroundColor: colors.border }]} />
            
            <TouchableOpacity style={styles.settingsItem}>
              <View style={styles.settingsLabelWithIcon}>
                <CreditCard size={18} color={colors.text} style={styles.settingsIcon} />
                <Text style={[styles.settingsLabel, { color: colors.text }]}>About</Text>
              </View>
              <ChevronRight size={20} color={colors.textSecondary} />
            </TouchableOpacity>
          </View>
        </View>

        {/* Sign Out Button */}
        {isAuthenticated && (
          <TouchableOpacity 
            style={[styles.signOutButton, { backgroundColor: colors.error + '15' }]}
            onPress={handleSignOut}
          >
            <LogOut size={18} color={colors.error} style={styles.signOutIcon} />
            <Text style={[styles.signOutText, { color: colors.error }]}>Sign Out</Text>
          </TouchableOpacity>
        )}

        <View style={styles.versionContainer}>
          <Text style={[styles.versionText, { color: colors.textSecondary }]}>Version 1.0.0</Text>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingHorizontal: 16,
  },
  profileSection: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 16,
    borderRadius: 12,
    marginTop: 16,
    marginBottom: 24,
  },
  profileInfo: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  profileImage: {
    width: 50,
    height: 50,
    borderRadius: 25,
  },
  profileImagePlaceholder: {
    width: 50,
    height: 50,
    borderRadius: 25,
    alignItems: 'center',
    justifyContent: 'center',
  },
  profileText: {
    marginLeft: 12,
  },
  profileName: {
    fontSize: 16,
    fontWeight: '600',
  },
  profileEmail: {
    fontSize: 14,
    marginTop: 2,
  },
  loginText: {
    fontSize: 16,
    fontWeight: '600',
    marginLeft: 12,
  },
  section: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 14,
    fontWeight: '600',
    marginBottom: 8,
    marginLeft: 4,
    textTransform: 'uppercase',
  },
  settingsGroup: {
    borderRadius: 12,
    overflow: 'hidden',
  },
  settingsItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 16,
  },
  settingsLabelWithIcon: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  settingsIcon: {
    marginRight: 12,
  },
  settingsLabel: {
    fontSize: 16,
  },
  settingsValue: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  settingsValueText: {
    fontSize: 16,
    marginRight: 8,
  },
  separator: {
    height: 1,
    marginHorizontal: 16,
  },
  signOutButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 16,
    borderRadius: 12,
    marginBottom: 16,
  },
  signOutIcon: {
    marginRight: 8,
  },
  signOutText: {
    fontSize: 16,
    fontWeight: '600',
  },
  versionContainer: {
    alignItems: 'center',
    marginBottom: 24,
  },
  versionText: {
    fontSize: 14,
  },
});