import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';
import { useTheme } from '@/context/ThemeContext';

export interface EmptyStateProps {
  title: string;
  message?: string;
  buttonText?: string;
  onPress?: () => void;
  icon?: React.ReactNode;
}

export default function EmptyState({ 
  title, 
  message, 
  buttonText, 
  onPress, 
  icon 
}: EmptyStateProps) {
  const { colors: themeColors } = useTheme();
  
  return (
    <View style={styles.container}>
      {icon && (
        <View style={[styles.iconContainer, { backgroundColor: themeColors.primary + '10' }]}>
          {icon}
        </View>
      )}
      
      <Text style={[styles.title, { color: themeColors.text }]}>
        {title}
      </Text>
      
      {message && (
        <Text style={[styles.message, { color: themeColors.textLight }]}>
          {message}
        </Text>
      )}
      
      {buttonText && onPress && (
        <TouchableOpacity 
          style={[styles.button, { backgroundColor: themeColors.primary }]}
          onPress={onPress}
        >
          <Text style={[styles.buttonText, { color: themeColors.background }]}>
            {buttonText}
          </Text>
        </TouchableOpacity>
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 24,
  },
  iconContainer: {
    width: 80,
    height: 80,
    borderRadius: 40,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 24,
  },
  title: {
    fontSize: 20,
    fontWeight: '600',
    marginBottom: 8,
    textAlign: 'center',
  },
  message: {
    fontSize: 16,
    textAlign: 'center',
    marginBottom: 24,
  },
  button: {
    paddingVertical: 12,
    paddingHorizontal: 24,
    borderRadius: 8,
  },
  buttonText: {
    fontSize: 16,
    fontWeight: '600',
  },
});