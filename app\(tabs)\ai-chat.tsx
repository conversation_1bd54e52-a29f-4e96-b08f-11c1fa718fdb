import React, { useState, useRef, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  KeyboardAvoidingView,
  Platform,
  Alert,
  Keyboard,
  Animated,
  Dimensions,
  Image,
  ActivityIndicator,
} from 'react-native';
import { Stack } from 'expo-router';
import { useTransactionStore } from '@/store/transactionStore';
import { formatCurrency } from '@/utils/formatters';
import { parseTransactionsFromText } from '@/utils/aiHelpers';
import { useTheme } from '@/context/ThemeContext';
import { useAnalytics } from '@/context/AnalyticsContext';
import { useNetworkStatus } from '@/hooks/useNetworkStatus';
import { StatusBar } from 'expo-status-bar';
import { 
  Check, 
  X, 
  AlertTriangle, 
  Calendar, 
  DollarSign, 
  Tag, 
  AlertCircle,
  MessageSquare,
  Sparkles,
  Clock,
  Zap,
  Send,
  Image as ImageIcon,
  Mic,
  Paperclip,
  Trash2,
} from 'lucide-react-native';
import { getCategory } from '@/constants/categories';
import { useToast } from '@/context/ToastContext';
import { LinearGradient } from 'expo-linear-gradient';
import * as ImagePicker from 'expo-image-picker';
import { TextInput } from 'react-native-gesture-handler';
import { ParsedTransaction } from '@/types/transaction';

// Define message types
interface Message {
  id: string;
  role: 'user' | 'assistant' | 'system';
  content: string;
  timestamp: Date;
}

export default function AIChatScreen() {
  // State for messages
  const [messages, setMessages] = useState<Message[]>([
    {
      id: '1',
      role: 'assistant',
      content: "Hi there! I'm your AI financial assistant. You can ask me to track expenses, analyze your spending, or help with budgeting. For example, try saying 'I spent $45 on groceries yesterday and $20 on coffee today' or 'How much did I spend on dining last month?'",
      timestamp: new Date(),
    },
  ]);
  
  // State for input and loading
  const [inputMessage, setInputMessage] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [image, setImage] = useState<string | null>(null);
  
  // State for transactions
  const [parsedTransactions, setParsedTransactions] = useState<ParsedTransaction[]>([]);
  const [showTransactionPanel, setShowTransactionPanel] = useState(false);
  const [selectedTransactions, setSelectedTransactions] = useState<string[]>([]);
  
  // Refs and animations
  const scrollViewRef = useRef<ScrollView>(null);
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const slideAnim = useRef(new Animated.Value(50)).current;
  const panelHeight = useRef(new Animated.Value(0)).current;
  
  // Hooks
  const { addTransaction } = useTransactionStore();
  const { theme, colors } = useTheme();
  const { trackEvent, trackScreen } = useAnalytics();
  const { isConnected } = useNetworkStatus();
  const { showToast } = useToast();
  
  // Screen dimensions
  const windowHeight = Dimensions.get('window').height;
  const maxPanelHeight = windowHeight * 0.6;
  
  // Track screen view
  useEffect(() => {
    trackScreen('AI Chat');
    
    // Initial animations
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 400,
        useNativeDriver: true,
      }),
      Animated.timing(slideAnim, {
        toValue: 0,
        duration: 400,
        useNativeDriver: true,
      }),
    ]).start();
  }, []);
  
  // Scroll to bottom when messages change
  useEffect(() => {
    setTimeout(() => {
      scrollViewRef.current?.scrollToEnd({ animated: true });
    }, 100);
  }, [messages, showTransactionPanel]);
  
  // Animate transaction panel
  useEffect(() => {
    Animated.timing(panelHeight, {
      toValue: showTransactionPanel ? 1 : 0,
      duration: 300,
      useNativeDriver: false,
    }).start();
  }, [showTransactionPanel]);
  
  // Handle sending a message
  const handleSendMessage = async () => {
    if (!inputMessage.trim() && !image) return;
    
    if (!isConnected) {
      Alert.alert(
        "No Internet Connection",
        "You need to be connected to the internet to use the AI assistant."
      );
      return;
    }
    
    // Create user message
    const userMessage: Message = {
      id: Date.now().toString(),
      role: 'user',
      content: inputMessage.trim(),
      timestamp: new Date(),
    };
    
    // Add user message to chat
    setMessages(prev => [...prev, userMessage]);
    setIsLoading(true);
    setInputMessage('');
    setImage(null);
    Keyboard.dismiss();
    
    try {
      // Prepare messages for the AI
      const messagesToSend = [
        {
          role: 'system',
          content: `You are a helpful financial assistant that helps users track their expenses and income. 
          Current date: ${new Date().toISOString().split('T')[0]}
          
          IMPORTANT: If the user mentions multiple transactions (expenses or income), extract the details for EACH transaction SEPARATELY.
          For example, if they say "I spent $50 on food yesterday and $20 on fuel today", identify these as TWO separate transactions.
          
          For relative dates like "yesterday", "today", "last week", etc., convert them to actual dates.
          Always use the current year for dates unless explicitly specified otherwise.
          
          When extracting descriptions, DO NOT include date references in the description.
          For example, if user says "I spent $20 on coffee yesterday", the description should be just "coffee", not "coffee yesterday".
          
          If you're not sure about any field, make a reasonable guess based on the context.
          
          Available expense categories: Food & Dining, Transportation, Housing & Rent, Entertainment, Shopping, Utilities, Healthcare, Other Expense
          Available income categories: Salary, Freelance, Investments, Gifts, Other Income
          
          Always map to one of these categories exactly.
          
          When responding, acknowledge each transaction separately in your response.
          
          Format your response with clear transaction blocks like this:
          
          1. **Expense Transaction:**
             - **Amount:** $XX.XX
             - **Date:** YYYY-MM-DD
             - **Description:** Brief description
             - **Category:** Category name
          
          This format helps me extract the transaction data accurately.`
        },
        ...messages
          .filter(m => m.role === 'user' || m.role === 'assistant')
          .slice(-5)
          .map(m => ({ role: m.role, content: m.content })),
        { role: 'user', content: inputMessage.trim() }
      ];
      
      // Make API request
      const response = await fetch('https://toolkit.rork.com/text/llm/', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ messages: messagesToSend }),
      });
      
      if (!response.ok) {
        throw new Error('Failed to get response from AI');
      }
      
      const data = await response.json();
      const aiResponse = data.completion;
      
      // Create AI message
      const assistantMessage: Message = {
        id: Date.now().toString() + '1',
        role: 'assistant',
        content: aiResponse,
        timestamp: new Date(),
      };
      
      // Add AI message to chat
      setMessages(prev => [...prev, assistantMessage]);
      
      // Parse transactions from AI response
      const transactions = parseTransactionsFromText(aiResponse);
      
      if (transactions.length > 0) {
        // Add unique IDs to transactions
        const transactionsWithIds = transactions.map(t => ({
          ...t,
          id: Date.now().toString() + Math.random().toString(36).substring(2, 9)
        }));
        
        setParsedTransactions(transactionsWithIds);
        setSelectedTransactions(transactionsWithIds.map(t => t.id)); // Select all by default
        setShowTransactionPanel(true);
        
        trackEvent('transactions_extracted', { count: transactionsWithIds.length });
      }
    } catch (error) {
      console.error('Error sending message:', error);
      
      // Add error message
      const errorMessage: Message = {
        id: Date.now().toString() + '1',
        role: 'assistant',
        content: "I'm sorry, I couldn't process your request. Please try again later.",
        timestamp: new Date(),
      };
      
      setMessages(prev => [...prev, errorMessage]);
      
      showToast({
        message: "Failed to connect to AI service. Please try again.",
        type: "error",
        duration: 3000
      });
    } finally {
      setIsLoading(false);
    }
  };
  
  // Handle image picking
  const handlePickImage = async () => {
    try {
      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: true,
        aspect: [4, 3],
        quality: 0.8,
      });
      
      if (!result.canceled && result.assets && result.assets.length > 0) {
        setImage(result.assets[0].uri);
      }
    } catch (error) {
      console.error('Error picking image:', error);
    }
  };
  
  // Handle transaction selection
  const toggleTransactionSelection = (id: string) => {
    setSelectedTransactions(prev => {
      if (prev.includes(id)) {
        return prev.filter(transactionId => transactionId !== id);
      } else {
        return [...prev, id];
      }
    });
  };
  
  // Handle select/deselect all transactions
  const toggleSelectAll = () => {
    if (selectedTransactions.length === parsedTransactions.length) {
      // Deselect all
      setSelectedTransactions([]);
    } else {
      // Select all
      setSelectedTransactions(parsedTransactions.map(t => t.id));
    }
  };
  
  // Handle confirming selected transactions
  const confirmSelectedTransactions = async () => {
    if (selectedTransactions.length === 0) {
      showToast({
        message: "No transactions selected",
        type: "warning",
        duration: 2000
      });
      return;
    }
    
    const selectedTransactionsData = parsedTransactions.filter(
      t => selectedTransactions.includes(t.id)
    );
    
    let successCount = 0;
    
    for (const transaction of selectedTransactionsData) {
      try {
        await addTransaction({
          amount: transaction.amount,
          description: transaction.description,
          category: transaction.category,
          date: transaction.date,
          type: transaction.type,
        });
        successCount++;
      } catch (error) {
        console.error('Error adding transaction:', error);
      }
    }
    
    if (successCount > 0) {
      showToast({
        message: `Added ${successCount} transaction${successCount !== 1 ? 's' : ''} successfully!`,
        type: "success",
        duration: 2000
      });
      
      trackEvent('transactions_added_bulk', { count: successCount });
    }
    
    // Add confirmation message
    const confirmationMessage: Message = {
      id: Date.now().toString(),
      role: 'assistant',
      content: successCount === 0
        ? "No problem. I won't add any transactions. Is there anything else I can help you with?"
        : successCount === 1
          ? "Great! I've added the transaction you selected. Is there anything else I can help you with?"
          : `Great! I've added all ${successCount} transactions you selected. Is there anything else I can help you with?`,
      timestamp: new Date(),
    };
    
    setMessages(prev => [...prev, confirmationMessage]);
    setShowTransactionPanel(false);
    setParsedTransactions([]);
    setSelectedTransactions([]);
  };
  
  // Handle canceling all transactions
  const cancelAllTransactions = () => {
    // Add cancellation message
    const cancellationMessage: Message = {
      id: Date.now().toString(),
      role: 'assistant',
      content: "No problem. I won't add any transactions. Is there anything else I can help you with?",
      timestamp: new Date(),
    };
    
    setMessages(prev => [...prev, cancellationMessage]);
    setShowTransactionPanel(false);
    setParsedTransactions([]);
    setSelectedTransactions([]);
  };
  
  // Calculate panel height based on animation value
  const calculatedPanelHeight = panelHeight.interpolate({
    inputRange: [0, 1],
    outputRange: [0, Math.min(parsedTransactions.length * 120 + 180, maxPanelHeight)]
  });
  
  // Render a message bubble
  const renderMessage = (message: Message, index: number) => {
    const isUser = message.role === 'user';
    const isLastMessage = index === messages.length - 1;
    
    return (
      <Animated.View 
        key={message.id} 
        style={[
          styles.messageContainer,
          isUser ? styles.userMessageContainer : styles.aiMessageContainer,
          isLastMessage && { 
            opacity: fadeAnim, 
            transform: [{ translateY: slideAnim }] 
          }
        ]}
      >
        {!isUser && (
          <View style={styles.avatarContainer}>
            <LinearGradient
              colors={[colors.primary, colors.aiAccent]}
              style={styles.avatar}
              start={{ x: 0, y: 0 }}
              end={{ x: 1, y: 1 }}
            >
              <Sparkles size={14} color="#FFFFFF" />
            </LinearGradient>
          </View>
        )}
        
        <View style={[
          styles.messageBubble,
          isUser 
            ? [
                styles.userMessage, 
                { backgroundColor: colors.primary }
              ] 
            : [
                styles.aiMessage, 
                { backgroundColor: theme === 'dark' ? colors.aiBackground : colors.card }
              ],
        ]}>
          <Text style={[
            styles.messageText, 
            { color: isUser ? '#FFFFFF' : colors.text }
          ]}>
            {message.content}
          </Text>
          
          <View style={styles.messageFooter}>
            <View style={styles.timestampContainer}>
              <Clock size={12} color={isUser ? 'rgba(255, 255, 255, 0.7)' : colors.textLight} style={{ marginRight: 4 }} />
              <Text style={[
                styles.timestamp, 
                { color: isUser ? 'rgba(255, 255, 255, 0.7)' : colors.textLight }
              ]}>
                {message.timestamp.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
              </Text>
            </View>
          </View>
        </View>
        
        {isUser && (
          <View style={styles.userAvatarSpacer} />
        )}
      </Animated.View>
    );
  };
  
  // Render a transaction card
  const renderTransactionCard = (transaction: ParsedTransaction) => {
    const isSelected = selectedTransactions.includes(transaction.id);
    const category = getCategory(transaction.category);
    
    // Validate transaction data
    const hasInvalidAmount = transaction.amount <= 0 && transaction.type === 'expense';
    const hasInvalidDate = isNaN(new Date(transaction.date).getTime());
    const hasInvalidCategory = !category;
    const hasValidationErrors = hasInvalidAmount || hasInvalidDate || hasInvalidCategory;
    
    return (
      <TouchableOpacity
        key={transaction.id}
        style={[
          styles.transactionCard,
          {
            backgroundColor: isSelected
              ? theme === 'dark'
                ? colors.primary + '30'
                : colors.primary + '15'
              : theme === 'dark'
                ? colors.card
                : colors.background,
            borderColor: isSelected
              ? colors.primary
              : colors.border,
          }
        ]}
        onPress={() => toggleTransactionSelection(transaction.id)}
        activeOpacity={0.7}
      >
        <View style={styles.transactionHeader}>
          <View style={styles.transactionInfo}>
            <View style={styles.amountRow}>
              <DollarSign
                size={16}
                color={
                  hasInvalidAmount
                    ? colors.danger
                    : transaction.type === 'expense'
                      ? colors.expense
                      : colors.income
                }
              />
              <Text
                style={[
                  styles.transactionAmount,
                  {
                    color: hasInvalidAmount
                      ? colors.danger
                      : transaction.type === 'expense'
                        ? colors.expense
                        : colors.income
                  }
                ]}
              >
                {formatCurrency(transaction.amount)}
              </Text>
              <Text style={[styles.transactionType, { color: colors.textLight }]}>
                ({transaction.type})
              </Text>
            </View>

            <Text style={[styles.transactionDescription, { color: colors.text }]}>
              {transaction.description}
            </Text>

            <View style={styles.transactionMeta}>
              <View style={styles.categoryRow}>
                <Tag size={14} color={hasInvalidCategory ? colors.danger : colors.textLight} />
                {category && (
                  <View style={[styles.categoryDot, { backgroundColor: category.color }]} />
                )}
                <Text
                  style={[
                    styles.categoryText,
                    { color: hasInvalidCategory ? colors.danger : colors.textLight }
                  ]}
                >
                  {category ? category.name : 'Invalid Category'}
                </Text>
              </View>

              <View style={styles.dateRow}>
                <Calendar
                  size={14}
                  color={hasInvalidDate ? colors.danger : colors.textLight}
                />
                <Text
                  style={[
                    styles.dateText,
                    { color: hasInvalidDate ? colors.danger : colors.textLight }
                  ]}
                >
                  {hasInvalidDate
                    ? 'Invalid Date'
                    : new Date(transaction.date).toLocaleDateString()}
                </Text>
              </View>
            </View>

            {hasValidationErrors && (
              <View style={[styles.validationWarning, { backgroundColor: colors.danger + '15' }]}>
                <AlertCircle size={14} color={colors.danger} />
                <Text style={[styles.validationText, { color: colors.danger }]}>
                  This transaction has validation errors
                </Text>
              </View>
            )}
          </View>

          <View style={styles.checkboxContainer}>
            <View
              style={[
                styles.checkbox,
                {
                  borderColor: isSelected ? colors.primary : colors.border,
                  backgroundColor: isSelected ? colors.primary : 'transparent',
                }
              ]}
            >
              {isSelected && <Check size={14} color="#FFFFFF" />}
            </View>
          </View>
        </View>
      </TouchableOpacity>
    );
  };

  return (
    <>
      <Stack.Screen
        options={{
          title: 'AI Assistant',
          headerStyle: {
            backgroundColor: colors.background,
          },
          headerTintColor: colors.text,
          headerShadowVisible: false,
        }}
      />

      <StatusBar style={theme === 'dark' ? 'light' : 'dark'} />

      <KeyboardAvoidingView
        style={{ flex: 1 }}
        behavior={Platform.OS === 'ios' ? 'padding' : undefined}
        keyboardVerticalOffset={Platform.OS === 'ios' ? 90 : 0}
      >
        <View style={[styles.container, { backgroundColor: colors.background }]}>
          {!isConnected && (
            <View style={[styles.offlineBanner, { backgroundColor: colors.warning + '20' }]}>
              <AlertTriangle size={16} color={colors.warning} />
              <Text style={[styles.offlineText, { color: colors.warning }]}>
                You're offline. AI features are limited.
              </Text>
            </View>
          )}

          <ScrollView
            ref={scrollViewRef}
            style={styles.messagesContainer}
            contentContainerStyle={styles.messagesContent}
          >
            {messages.map(renderMessage)}

            {isLoading && (
              <View style={styles.loadingContainer}>
                <View style={styles.avatarContainer}>
                  <LinearGradient
                    colors={[colors.primary, colors.aiAccent]}
                    style={styles.avatar}
                    start={{ x: 0, y: 0 }}
                    end={{ x: 1, y: 1 }}
                  >
                    <Sparkles size={14} color="#FFFFFF" />
                  </LinearGradient>
                </View>

                <View style={[
                  styles.loadingBubble,
                  { backgroundColor: theme === 'dark' ? colors.aiBackground : colors.card }
                ]}>
                  <View style={styles.loadingDots}>
                    <Animated.View style={[
                      styles.loadingDot,
                      { backgroundColor: colors.primary }
                    ]} />
                    <Animated.View style={[
                      styles.loadingDot,
                      { backgroundColor: colors.primary }
                    ]} />
                    <Animated.View style={[
                      styles.loadingDot,
                      { backgroundColor: colors.primary }
                    ]} />
                  </View>
                </View>
              </View>
            )}
          </ScrollView>

          {/* Transaction Panel */}
          {showTransactionPanel && (
            <Animated.View
              style={[
                styles.transactionPanel,
                {
                  backgroundColor: theme === 'dark'
                    ? 'rgba(30, 30, 30, 0.95)'
                    : 'rgba(255, 255, 255, 0.95)',
                  borderColor: colors.border,
                  height: calculatedPanelHeight,
                }
              ]}
            >
              <LinearGradient
                colors={[
                  colors.primary + '20',
                  'transparent'
                ]}
                style={styles.panelGradient}
                start={{ x: 0, y: 0 }}
                end={{ x: 0, y: 0.5 }}
              >
                <View style={styles.panelHeader}>
                  <View style={styles.panelTitleContainer}>
                    <Zap size={18} color={colors.primary} style={{ marginRight: 8 }} />
                    <Text style={[styles.panelTitle, { color: colors.text }]}>
                      Confirm Transactions
                    </Text>
                  </View>

                  <Text style={[styles.transactionCount, { color: colors.textLight }]}>
                    {parsedTransactions.length} transaction{parsedTransactions.length !== 1 ? 's' : ''} extracted
                  </Text>
                </View>

                <View style={styles.selectionHeader}>
                  <TouchableOpacity
                    style={[styles.selectAllButton, {
                      backgroundColor: colors.primary + '20',
                      borderColor: colors.primary + '40',
                    }]}
                    onPress={toggleSelectAll}
                  >
                    <Text style={[styles.selectAllText, { color: colors.primary }]}>
                      {selectedTransactions.length === parsedTransactions.length
                        ? 'Deselect All'
                        : 'Select All'}
                    </Text>
                  </TouchableOpacity>

                  <Text style={[styles.selectedCount, { color: colors.textLight }]}>
                    {selectedTransactions.length} of {parsedTransactions.length} selected
                  </Text>
                </View>

                <ScrollView
                  style={styles.transactionsList}
                  showsVerticalScrollIndicator={true}
                  nestedScrollEnabled={true}
                >
                  {parsedTransactions.map(renderTransactionCard)}
                </ScrollView>

                <View style={styles.panelActions}>
                  <TouchableOpacity
                    style={[styles.panelButton, { backgroundColor: colors.danger + '20' }]}
                    onPress={cancelAllTransactions}
                  >
                    <X size={18} color={colors.danger} />
                    <Text style={[styles.panelButtonText, { color: colors.danger }]}>
                      Cancel
                    </Text>
                  </TouchableOpacity>

                  <TouchableOpacity
                    style={[
                      styles.panelButton,
                      {
                        backgroundColor: selectedTransactions.length > 0
                          ? colors.success + '20'
                          : colors.textLight + '20',
                        opacity: selectedTransactions.length > 0 ? 1 : 0.7
                      }
                    ]}
                    onPress={confirmSelectedTransactions}
                    disabled={selectedTransactions.length === 0}
                  >
                    <Check size={18} color={selectedTransactions.length > 0 ? colors.success : colors.textLight} />
                    <Text
                      style={[
                        styles.panelButtonText,
                        {
                          color: selectedTransactions.length > 0
                            ? colors.success
                            : colors.textLight
                        }
                      ]}
                    >
                      Confirm {selectedTransactions.length > 0 ? `(${selectedTransactions.length})` : ''}
                    </Text>
                  </TouchableOpacity>
                </View>
              </LinearGradient>
            </Animated.View>
          )}

          {/* Chat Input */}
          <View style={[
            styles.inputContainer,
            {
              borderTopColor: theme === 'dark' ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.05)',
              backgroundColor: theme === 'dark' ? colors.card : colors.background,
            }
          ]}>
            {image && (
              <View style={styles.imagePreviewContainer}>
                <Image source={{ uri: image }} style={styles.imagePreview} />
                <TouchableOpacity
                  style={[styles.clearImageButton, { backgroundColor: colors.danger }]}
                  onPress={() => setImage(null)}
                >
                  <X size={16} color="#FFFFFF" />
                </TouchableOpacity>
              </View>
            )}

            <View style={[
              styles.inputRow,
              {
                backgroundColor: theme === 'dark' ? colors.aiBackground : colors.card,
              }
            ]}>
              <View style={styles.inputActions}>
                <TouchableOpacity
                  style={styles.inputActionButton}
                  onPress={handlePickImage}
                  disabled={isLoading || !isConnected}
                >
                  <ImageIcon
                    size={22}
                    color={!isConnected ? colors.textLight : colors.primary}
                    style={{ opacity: !isConnected ? 0.5 : 0.8 }}
                  />
                </TouchableOpacity>

                <TouchableOpacity
                  style={styles.inputActionButton}
                  disabled={isLoading || !isConnected}
                >
                  <Paperclip
                    size={22}
                    color={!isConnected ? colors.textLight : colors.primary}
                    style={{ opacity: !isConnected ? 0.5 : 0.7 }}
                  />
                </TouchableOpacity>
              </View>

              <TextInput
                style={[
                  styles.textInput,
                  {
                    color: colors.text,
                    backgroundColor: theme === 'dark' ? 'rgba(0,0,0,0.2)' : 'rgba(255,255,255,0.8)',
                  },
                ]}
                value={inputMessage}
                onChangeText={setInputMessage}
                placeholder="Ask me anything about your finances..."
                placeholderTextColor={colors.textLight}
                multiline
                maxLength={500}
                editable={!isLoading && isConnected}
              />

              <TouchableOpacity
                style={[
                  styles.sendButton,
                  (!inputMessage.trim() && !image) ? styles.micButton : styles.sendActiveButton,
                ]}
                onPress={handleSendMessage}
                disabled={isLoading || !isConnected || (!inputMessage.trim() && !image)}
              >
                {isLoading ? (
                  <ActivityIndicator size="small" color="#FFFFFF" />
                ) : !inputMessage.trim() && !image ? (
                  <Mic size={20} color={colors.primary} />
                ) : (
                  <LinearGradient
                    colors={[colors.primary, colors.aiAccent]}
                    style={styles.sendGradient}
                    start={{ x: 0, y: 0 }}
                    end={{ x: 1, y: 1 }}
                  >
                    <Send size={18} color="#FFFFFF" />
                  </LinearGradient>
                )}
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </KeyboardAvoidingView>
    </>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  offlineBanner: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 8,
    paddingHorizontal: 16,
  },
  offlineText: {
    marginLeft: 8,
    fontSize: 14,
    fontWeight: '500',
  },
  messagesContainer: {
    flex: 1,
  },
  messagesContent: {
    padding: 16,
    paddingBottom: 24,
  },
  messageContainer: {
    flexDirection: 'row',
    marginBottom: 20,
    alignItems: 'flex-end',
  },
  userMessageContainer: {
    justifyContent: 'flex-end',
  },
  aiMessageContainer: {
    justifyContent: 'flex-start',
  },
  avatarContainer: {
    width: 28,
    height: 28,
    marginRight: 8,
    borderRadius: 14,
    overflow: 'hidden',
  },
  avatar: {
    width: 28,
    height: 28,
    alignItems: 'center',
    justifyContent: 'center',
  },
  userAvatarSpacer: {
    width: 28,
    marginLeft: 8,
  },
  messageBubble: {
    maxWidth: '75%',
    padding: 14,
    borderRadius: 18,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  userMessage: {
    borderBottomRightRadius: 4,
  },
  aiMessage: {
    borderBottomLeftRadius: 4,
  },
  messageText: {
    fontSize: 16,
    lineHeight: 22,
  },
  messageFooter: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    marginTop: 6,
  },
  timestampContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  timestamp: {
    fontSize: 11,
  },
  loadingContainer: {
    flexDirection: 'row',
    alignItems: 'flex-end',
    marginBottom: 20,
  },
  loadingBubble: {
    padding: 14,
    borderRadius: 18,
    borderBottomLeftRadius: 4,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  loadingDots: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    height: 24,
    width: 60,
  },
  loadingDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    marginHorizontal: 4,
  },
  transactionPanel: {
    position: 'absolute',
    bottom: 80,
    left: 16,
    right: 16,
    borderRadius: 20,
    borderWidth: 1,
    overflow: 'hidden',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.2,
    shadowRadius: 12,
    elevation: 8,
  },
  panelGradient: {
    padding: 16,
    height: '100%',
  },
  panelHeader: {
    marginBottom: 16,
  },
  panelTitleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
  },
  panelTitle: {
    fontSize: 18,
    fontWeight: '600',
  },
  transactionCount: {
    fontSize: 14,
  },
  selectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  selectAllButton: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 8,
    borderWidth: 1,
  },
  selectAllText: {
    fontSize: 13,
    fontWeight: '500',
  },
  selectedCount: {
    fontSize: 13,
  },
  transactionsList: {
    maxHeight: 300,
  },
  transactionCard: {
    borderRadius: 16,
    padding: 14,
    marginBottom: 12,
    borderWidth: 1,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 6,
    elevation: 3,
  },
  transactionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
  },
  transactionInfo: {
    flex: 1,
    marginRight: 12,
  },
  amountRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 6,
  },
  transactionAmount: {
    fontSize: 17,
    fontWeight: '600',
    marginLeft: 4,
  },
  transactionType: {
    fontSize: 12,
    marginLeft: 6,
  },
  transactionDescription: {
    fontSize: 15,
    fontWeight: '500',
    marginBottom: 10,
  },
  transactionMeta: {
    gap: 6,
  },
  categoryRow: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  categoryDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    marginLeft: 6,
    marginRight: 4,
  },
  categoryText: {
    fontSize: 13,
  },
  dateRow: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  dateText: {
    fontSize: 13,
    marginLeft: 6,
  },
  validationWarning: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 8,
    borderRadius: 8,
    marginTop: 10,
  },
  validationText: {
    fontSize: 12,
    marginLeft: 6,
    fontWeight: '500',
  },
  checkboxContainer: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  checkbox: {
    width: 24,
    height: 24,
    borderRadius: 12,
    borderWidth: 2,
    justifyContent: 'center',
    alignItems: 'center',
  },
  panelActions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 16,
    gap: 12,
  },
  panelButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 14,
    borderRadius: 12,
  },
  panelButtonText: {
    marginLeft: 8,
    fontWeight: '600',
    fontSize: 15,
  },
  inputContainer: {
    padding: 12,
    borderTopWidth: 1,
  },
  imagePreviewContainer: {
    marginBottom: 12,
    borderRadius: 16,
    overflow: 'hidden',
    position: 'relative',
    alignSelf: 'flex-start',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.15,
    shadowRadius: 8,
    elevation: 4,
  },
  imagePreview: {
    width: 140,
    height: 140,
    borderRadius: 16,
  },
  clearImageButton: {
    position: 'absolute',
    top: 8,
    right: 8,
    width: 28,
    height: 28,
    borderRadius: 14,
    justifyContent: 'center',
    alignItems: 'center',
  },
  inputRow: {
    flexDirection: 'row',
    alignItems: 'center',
    borderRadius: 24,
    paddingHorizontal: 8,
    paddingVertical: 4,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 3,
  },
  inputActions: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  inputActionButton: {
    padding: 8,
  },
  textInput: {
    flex: 1,
    fontSize: 16,
    paddingHorizontal: 12,
    paddingVertical: 10,
    borderRadius: 20,
    marginHorizontal: 4,
    maxHeight: 100,
  },
  sendButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginLeft: 4,
    marginRight: 4,
  },
  sendActiveButton: {
    backgroundColor: 'transparent',
  },
  micButton: {
    backgroundColor: 'rgba(0, 0, 0, 0.05)',
  },
  sendGradient: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
});