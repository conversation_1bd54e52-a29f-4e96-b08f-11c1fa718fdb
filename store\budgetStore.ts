import { create } from 'zustand';
import { persist, createJSONStorage } from 'zustand/middleware';
import AsyncStorage from '@react-native-async-storage/async-storage';

interface BudgetState {
  budgets: Record<string, number>; // categoryId -> budget amount
  setBudget: (categoryId: string, amount: number) => void;
  removeBudget: (categoryId: string) => void;
  getBudget: (categoryId: string) => number;
}

export const useBudgetStore = create<BudgetState>()(
  persist(
    (set, get) => ({
      budgets: {},

      setBudget: (categoryId: string, amount: number) => set(state => ({
        budgets: {
          ...state.budgets,
          [categoryId]: amount
        }
      })),

      removeBudget: (categoryId: string) => set(state => {
        const newBudgets = { ...state.budgets };
        delete newBudgets[categoryId];
        return { budgets: newBudgets };
      }),

      getBudget: (categoryId: string) => {
        return get().budgets[categoryId] || 0;
      }
    }),
    {
      name: 'budget-storage',
      storage: createJSONStorage(() => AsyncStorage),
    }
  )
);

// Export alias for backward compatibility
export const useBudget = useBudgetStore;