import { Transaction } from '@/types/transaction';
import { getCategory } from '@/constants/categories';

export interface SpendingInsight {
  type: 'warning' | 'info' | 'success' | 'tip';
  title: string;
  message: string;
  icon: string;
  value?: string;
}

export interface BudgetRecommendation {
  category: string;
  currentSpending: number;
  recommendedBudget: number;
  reason: string;
  priority: 'high' | 'medium' | 'low';
}

export interface SpendingPattern {
  pattern: string;
  description: string;
  trend: 'increasing' | 'decreasing' | 'stable';
  impact: 'positive' | 'negative' | 'neutral';
}

export interface FinancialHealthScore {
  score: number; // 0-100
  grade: 'A' | 'B' | 'C' | 'D' | 'F';
  factors: {
    savingsRate: number;
    expenseVariability: number;
    categoryBalance: number;
    trendConsistency: number;
  };
  recommendations: string[];
}

/**
 * Generate intelligent spending insights from transactions
 */
export function generateSpendingInsights(transactions: Transaction[]): SpendingInsight[] {
  const insights: SpendingInsight[] = [];
  
  if (transactions.length === 0) {
    return [{
      type: 'info',
      title: 'Start Tracking',
      message: 'Add some transactions to see personalized insights about your spending patterns.',
      icon: 'info'
    }];
  }

  const now = new Date();
  const thisMonth = transactions.filter(t => {
    const transactionDate = new Date(t.date);
    return transactionDate.getMonth() === now.getMonth() && 
           transactionDate.getFullYear() === now.getFullYear();
  });

  const lastMonth = transactions.filter(t => {
    const transactionDate = new Date(t.date);
    const lastMonthDate = new Date(now.getFullYear(), now.getMonth() - 1);
    return transactionDate.getMonth() === lastMonthDate.getMonth() && 
           transactionDate.getFullYear() === lastMonthDate.getFullYear();
  });

  // Monthly spending comparison
  const thisMonthExpenses = thisMonth.filter(t => t.type === 'expense').reduce((sum, t) => sum + t.amount, 0);
  const lastMonthExpenses = lastMonth.filter(t => t.type === 'expense').reduce((sum, t) => sum + t.amount, 0);
  
  if (lastMonthExpenses > 0) {
    const changePercent = ((thisMonthExpenses - lastMonthExpenses) / lastMonthExpenses) * 100;
    
    if (changePercent > 20) {
      insights.push({
        type: 'warning',
        title: 'Spending Spike',
        message: `Your spending increased by ${changePercent.toFixed(1)}% compared to last month.`,
        icon: 'trending-up',
        value: `+${changePercent.toFixed(1)}%`
      });
    } else if (changePercent < -10) {
      insights.push({
        type: 'success',
        title: 'Great Savings!',
        message: `You reduced spending by ${Math.abs(changePercent).toFixed(1)}% this month.`,
        icon: 'trending-down',
        value: `-${Math.abs(changePercent).toFixed(1)}%`
      });
    }
  }

  // Category analysis
  const categorySpending = thisMonth
    .filter(t => t.type === 'expense')
    .reduce((acc, t) => {
      acc[t.category] = (acc[t.category] || 0) + t.amount;
      return acc;
    }, {} as Record<string, number>);

  const topCategory = Object.entries(categorySpending)
    .sort(([,a], [,b]) => b - a)[0];

  if (topCategory && topCategory[1] > thisMonthExpenses * 0.4) {
    const category = getCategory(topCategory[0]);
    insights.push({
      type: 'warning',
      title: 'Category Dominance',
      message: `${category?.name || 'Unknown'} accounts for ${((topCategory[1] / thisMonthExpenses) * 100).toFixed(1)}% of your spending.`,
      icon: 'pie-chart'
    });
  }

  // Income vs Expenses
  const thisMonthIncome = thisMonth.filter(t => t.type === 'income').reduce((sum, t) => sum + t.amount, 0);
  const savingsRate = thisMonthIncome > 0 ? ((thisMonthIncome - thisMonthExpenses) / thisMonthIncome) * 100 : 0;

  if (savingsRate < 10 && thisMonthIncome > 0) {
    insights.push({
      type: 'warning',
      title: 'Low Savings Rate',
      message: `You're only saving ${savingsRate.toFixed(1)}% of your income. Aim for at least 20%.`,
      icon: 'alert-triangle'
    });
  } else if (savingsRate > 30) {
    insights.push({
      type: 'success',
      title: 'Excellent Savings!',
      message: `You're saving ${savingsRate.toFixed(1)}% of your income. Keep it up!`,
      icon: 'check-circle'
    });
  }

  // Frequent small expenses
  const smallExpenses = thisMonth.filter(t => t.type === 'expense' && t.amount < 20);
  if (smallExpenses.length > 15) {
    const totalSmall = smallExpenses.reduce((sum, t) => sum + t.amount, 0);
    insights.push({
      type: 'tip',
      title: 'Small Expenses Add Up',
      message: `${smallExpenses.length} small purchases totaled $${totalSmall.toFixed(2)} this month.`,
      icon: 'coffee'
    });
  }

  return insights;
}

/**
 * Generate budget recommendations based on spending patterns
 */
export function generateBudgetRecommendations(transactions: Transaction[]): BudgetRecommendation[] {
  const recommendations: BudgetRecommendation[] = [];
  
  // Analyze last 3 months of data
  const threeMonthsAgo = new Date();
  threeMonthsAgo.setMonth(threeMonthsAgo.getMonth() - 3);
  
  const recentTransactions = transactions.filter(t => 
    new Date(t.date) >= threeMonthsAgo && t.type === 'expense'
  );

  // Group by category and calculate averages
  const categoryData = recentTransactions.reduce((acc, t) => {
    if (!acc[t.category]) {
      acc[t.category] = { amounts: [], total: 0 };
    }
    acc[t.category].amounts.push(t.amount);
    acc[t.category].total += t.amount;
    return acc;
  }, {} as Record<string, { amounts: number[], total: number }>);

  Object.entries(categoryData).forEach(([categoryId, data]) => {
    const category = getCategory(categoryId);
    if (!category) return;

    const average = data.total / 3; // 3 months average
    const variance = calculateVariance(data.amounts);
    
    let recommendedBudget = average;
    let reason = '';
    let priority: 'high' | 'medium' | 'low' = 'medium';

    if (variance > average * 0.5) {
      // High variance - suggest higher budget with buffer
      recommendedBudget = average * 1.3;
      reason = 'High spending variability detected. Added 30% buffer.';
      priority = 'high';
    } else if (variance < average * 0.2) {
      // Low variance - can optimize
      recommendedBudget = average * 1.1;
      reason = 'Consistent spending pattern. Small buffer added.';
      priority = 'low';
    } else {
      // Normal variance
      recommendedBudget = average * 1.2;
      reason = 'Based on 3-month average with 20% buffer.';
      priority = 'medium';
    }

    recommendations.push({
      category: category.name,
      currentSpending: average,
      recommendedBudget,
      reason,
      priority
    });
  });

  return recommendations.sort((a, b) => {
    const priorityOrder = { high: 3, medium: 2, low: 1 };
    return priorityOrder[b.priority] - priorityOrder[a.priority];
  });
}

/**
 * Calculate variance of an array of numbers
 */
function calculateVariance(numbers: number[]): number {
  if (numbers.length === 0) return 0;
  
  const mean = numbers.reduce((sum, num) => sum + num, 0) / numbers.length;
  const squaredDiffs = numbers.map(num => Math.pow(num - mean, 2));
  return squaredDiffs.reduce((sum, diff) => sum + diff, 0) / numbers.length;
}

/**
 * Detect spending patterns
 */
export function detectSpendingPatterns(transactions: Transaction[]): SpendingPattern[] {
  const patterns: SpendingPattern[] = [];
  
  // Weekend vs Weekday spending
  const weekendSpending = transactions.filter(t => {
    const date = new Date(t.date);
    const day = date.getDay();
    return (day === 0 || day === 6) && t.type === 'expense';
  });
  
  const weekdaySpending = transactions.filter(t => {
    const date = new Date(t.date);
    const day = date.getDay();
    return (day >= 1 && day <= 5) && t.type === 'expense';
  });

  if (weekendSpending.length > 0 && weekdaySpending.length > 0) {
    const weekendAvg = weekendSpending.reduce((sum, t) => sum + t.amount, 0) / weekendSpending.length;
    const weekdayAvg = weekdaySpending.reduce((sum, t) => sum + t.amount, 0) / weekdaySpending.length;
    
    if (weekendAvg > weekdayAvg * 1.5) {
      patterns.push({
        pattern: 'Weekend Spender',
        description: 'You tend to spend significantly more on weekends',
        trend: 'stable',
        impact: 'negative'
      });
    }
  }

  return patterns;
}

/**
 * Calculate financial health score
 */
export function calculateFinancialHealthScore(transactions: Transaction[]): FinancialHealthScore {
  const recentTransactions = transactions.filter(t => {
    const threeMonthsAgo = new Date();
    threeMonthsAgo.setMonth(threeMonthsAgo.getMonth() - 3);
    return new Date(t.date) >= threeMonthsAgo;
  });

  const income = recentTransactions.filter(t => t.type === 'income').reduce((sum, t) => sum + t.amount, 0);
  const expenses = recentTransactions.filter(t => t.type === 'expense').reduce((sum, t) => sum + t.amount, 0);
  
  // Calculate factors (0-100 each)
  const savingsRate = income > 0 ? Math.min(((income - expenses) / income) * 100, 100) : 0;
  const expenseVariability = calculateExpenseVariability(recentTransactions);
  const categoryBalance = calculateCategoryBalance(recentTransactions);
  const trendConsistency = calculateTrendConsistency(recentTransactions);

  // Overall score (weighted average)
  const score = Math.round(
    (savingsRate * 0.4) + 
    (expenseVariability * 0.2) + 
    (categoryBalance * 0.2) + 
    (trendConsistency * 0.2)
  );

  let grade: 'A' | 'B' | 'C' | 'D' | 'F';
  if (score >= 90) grade = 'A';
  else if (score >= 80) grade = 'B';
  else if (score >= 70) grade = 'C';
  else if (score >= 60) grade = 'D';
  else grade = 'F';

  const recommendations: string[] = [];
  if (savingsRate < 20) recommendations.push('Increase your savings rate to at least 20%');
  if (expenseVariability < 60) recommendations.push('Try to maintain more consistent spending patterns');
  if (categoryBalance < 70) recommendations.push('Balance your spending across different categories');

  return {
    score,
    grade,
    factors: {
      savingsRate,
      expenseVariability,
      categoryBalance,
      trendConsistency
    },
    recommendations
  };
}

function calculateExpenseVariability(transactions: Transaction[]): number {
  const expenses = transactions.filter(t => t.type === 'expense');
  if (expenses.length < 2) return 100;
  
  const amounts = expenses.map(t => t.amount);
  const variance = calculateVariance(amounts);
  const mean = amounts.reduce((sum, amount) => sum + amount, 0) / amounts.length;
  
  // Lower variance is better (higher score)
  const coefficientOfVariation = mean > 0 ? variance / mean : 1;
  return Math.max(0, 100 - (coefficientOfVariation * 50));
}

function calculateCategoryBalance(transactions: Transaction[]): number {
  const expenses = transactions.filter(t => t.type === 'expense');
  if (expenses.length === 0) return 100;
  
  const categoryTotals = expenses.reduce((acc, t) => {
    acc[t.category] = (acc[t.category] || 0) + t.amount;
    return acc;
  }, {} as Record<string, number>);
  
  const total = Object.values(categoryTotals).reduce((sum, amount) => sum + amount, 0);
  const percentages = Object.values(categoryTotals).map(amount => (amount / total) * 100);
  
  // Penalize if any category is more than 50% of spending
  const maxPercentage = Math.max(...percentages);
  return Math.max(0, 100 - Math.max(0, maxPercentage - 50));
}

function calculateTrendConsistency(transactions: Transaction[]): number {
  // Simplified trend consistency calculation
  // In a real implementation, this would analyze month-over-month changes
  return 75; // Placeholder
}
