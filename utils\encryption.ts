import { Platform } from 'react-native';

// Simple encryption key (in a real app, this would be securely stored)
const ENCRYPTION_KEY = 'expense-tracker-encryption-key-2023';

// Simple XOR encryption for demo purposes
// In a production app, use a proper encryption library
export function encrypt(text: string): string {
  if (Platform.OS === 'web') {
    // Skip encryption on web for demo
    return text;
  }
  
  let result = '';
  for (let i = 0; i < text.length; i++) {
    const charCode = text.charCodeAt(i) ^ ENCRYPTION_KEY.charCodeAt(i % ENCRYPTION_KEY.length);
    result += String.fromCharCode(charCode);
  }
  return Buffer.from(result).toString('base64');
}

export function decrypt(encryptedText: string): string {
  if (Platform.OS === 'web') {
    // Skip decryption on web for demo
    return encryptedText;
  }
  
  const text = Buffer.from(encryptedText, 'base64').toString();
  let result = '';
  for (let i = 0; i < text.length; i++) {
    const charCode = text.charCodeAt(i) ^ ENCRYPTION_KEY.charCodeAt(i % ENCRYPTION_KEY.length);
    result += String.fromCharCode(charCode);
  }
  return result;
}