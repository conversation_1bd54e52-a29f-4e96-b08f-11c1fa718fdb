import React, { useState } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, ScrollView, Platform, Dimensions } from 'react-native';
import { Calendar, ChevronLeft, ChevronRight, X } from 'lucide-react-native';
import { useTheme } from '@/context/ThemeContext';

const { height: screenHeight } = Dimensions.get('window');

interface DateRangePickerProps {
  initialStartDate: string;
  initialEndDate: string;
  onSave: (startDate: string, endDate: string) => void;
  onCancel: () => void;
}

export default function DateRangePicker({
  initialStartDate,
  initialEndDate,
  onSave,
  onCancel
}: DateRangePickerProps) {
  const { colors: themeColors } = useTheme();
  const [startDate, setStartDate] = useState(initialStartDate);
  const [endDate, setEndDate] = useState(initialEndDate);
  const [selectingStartDate, setSelectingStartDate] = useState(true);
  const [currentMonth, setCurrentMonth] = useState(() => {
    const date = new Date(initialStartDate);
    return { year: date.getFullYear(), month: date.getMonth() };
  });
  
  // Generate days for the current month
  const generateDaysForMonth = (year: number, month: number) => {
    const daysInMonth = new Date(year, month + 1, 0).getDate();
    const firstDayOfMonth = new Date(year, month, 1).getDay();
    const days = [];
    
    // Add empty spaces for days before the 1st of the month
    for (let i = 0; i < firstDayOfMonth; i++) {
      days.push(null);
    }
    
    // Add days of the month
    for (let i = 1; i <= daysInMonth; i++) {
      days.push(i);
    }
    
    return days;
  };
  
  // Format date as YYYY-MM-DD
  const formatDate = (year: number, month: number, day: number) => {
    return `${year}-${String(month + 1).padStart(2, '0')}-${String(day).padStart(2, '0')}`;
  };
  
  // Check if a date is selected
  const isDateSelected = (dateStr: string) => {
    return dateStr === startDate || dateStr === endDate;
  };
  
  // Check if a date is in the selected range
  const isDateInRange = (dateStr: string) => {
    if (!startDate || !endDate) return false;
    return dateStr > startDate && dateStr < endDate;
  };
  
  // Check if a date is today
  const isToday = (year: number, month: number, day: number) => {
    const today = new Date();
    return (
      today.getFullYear() === year &&
      today.getMonth() === month &&
      today.getDate() === day
    );
  };
  
  // Handle date selection
  const handleDateSelect = (year: number, month: number, day: number) => {
    const selectedDate = formatDate(year, month, day);
    
    if (selectingStartDate) {
      setStartDate(selectedDate);
      setEndDate(selectedDate);
      setSelectingStartDate(false);
    } else {
      if (selectedDate < startDate) {
        setStartDate(selectedDate);
        setEndDate(startDate);
      } else {
        setEndDate(selectedDate);
      }
    }
  };
  
  // Handle save button press
  const handleSave = () => {
    onSave(startDate, endDate);
  };
  
  // Navigate to previous month
  const goToPreviousMonth = () => {
    setCurrentMonth(prev => {
      const newMonth = prev.month - 1;
      if (newMonth < 0) {
        return { year: prev.year - 1, month: 11 };
      }
      return { year: prev.year, month: newMonth };
    });
  };
  
  // Navigate to next month
  const goToNextMonth = () => {
    setCurrentMonth(prev => {
      const newMonth = prev.month + 1;
      if (newMonth > 11) {
        return { year: prev.year + 1, month: 0 };
      }
      return { year: prev.year, month: newMonth };
    });
  };
  
  // Get month name
  const getMonthName = (month: number) => {
    const monthNames = [
      'January', 'February', 'March', 'April', 'May', 'June',
      'July', 'August', 'September', 'October', 'November', 'December'
    ];
    return monthNames[month];
  };
  
  const days = generateDaysForMonth(currentMonth.year, currentMonth.month);
  
  return (
    <View style={[styles.overlay, { backgroundColor: 'rgba(0, 0, 0, 0.5)' }]}>
      <View style={[styles.container, { backgroundColor: themeColors.background }]}>
        {/* Header */}
        <View style={styles.header}>
          <View style={styles.headerLeft}>
            <Text style={[styles.title, { color: themeColors.text }]}>
              Select Date Range
            </Text>
            <Text style={[styles.subtitle, { color: themeColors.textLight }]}>
              {selectingStartDate ? 'Select start date' : 'Select end date'}
            </Text>
          </View>
          <TouchableOpacity
            style={[styles.closeButton, { backgroundColor: themeColors.card }]}
            onPress={onCancel}
          >
            <X size={20} color={themeColors.text} />
          </TouchableOpacity>
        </View>
        
        {/* Date Display */}
        <View style={styles.dateDisplay}>
          <View style={styles.dateItem}>
            <Text style={[styles.dateLabel, { color: themeColors.textLight }]}>Start Date</Text>
            <View style={[styles.dateValue, { backgroundColor: themeColors.primary + '20' }]}>
              <Calendar size={14} color={themeColors.primary} style={styles.dateIcon} />
              <Text style={[styles.dateText, { color: themeColors.primary }]}>
                {new Date(startDate).toLocaleDateString()}
              </Text>
            </View>
          </View>
          
          <View style={styles.dateSeparator}>
            <Text style={[styles.dateSeparatorText, { color: themeColors.textLight }]}>to</Text>
          </View>
          
          <View style={styles.dateItem}>
            <Text style={[styles.dateLabel, { color: themeColors.textLight }]}>End Date</Text>
            <View style={[styles.dateValue, { backgroundColor: themeColors.primary + '20' }]}>
              <Calendar size={14} color={themeColors.primary} style={styles.dateIcon} />
              <Text style={[styles.dateText, { color: themeColors.primary }]}>
                {new Date(endDate).toLocaleDateString()}
              </Text>
            </View>
          </View>
        </View>
        
        {/* Month Navigation */}
        <View style={styles.monthNavigation}>
          <TouchableOpacity
            style={[styles.navButton, { backgroundColor: themeColors.card }]}
            onPress={goToPreviousMonth}
          >
            <ChevronLeft size={20} color={themeColors.text} />
          </TouchableOpacity>
          
          <Text style={[styles.monthName, { color: themeColors.text }]}>
            {getMonthName(currentMonth.month)} {currentMonth.year}
          </Text>
          
          <TouchableOpacity
            style={[styles.navButton, { backgroundColor: themeColors.card }]}
            onPress={goToNextMonth}
          >
            <ChevronRight size={20} color={themeColors.text} />
          </TouchableOpacity>
        </View>
        
        {/* Calendar */}
        <View style={styles.calendarContainer}>
          <View style={styles.weekdayHeader}>
            {['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'].map((day) => (
              <Text key={day} style={[styles.weekdayText, { color: themeColors.textLight }]}>
                {day}
              </Text>
            ))}
          </View>
          
          <ScrollView 
            style={styles.daysScrollContainer}
            contentContainerStyle={styles.daysContainer}
            showsVerticalScrollIndicator={false}
          >
            {days.map((day, index) => {
              if (day === null) {
                return <View key={`empty-${index}`} style={styles.emptyDay} />;
              }
              
              const dateStr = formatDate(currentMonth.year, currentMonth.month, day);
              const isSelected = isDateSelected(dateStr);
              const isInRange = isDateInRange(dateStr);
              const isTodayDate = isToday(currentMonth.year, currentMonth.month, day);
              
              return (
                <TouchableOpacity
                  key={`day-${day}`}
                  style={[
                    styles.dayButton,
                    isSelected && [styles.selectedDay, { backgroundColor: themeColors.primary }],
                    isInRange && [styles.rangeDay, { backgroundColor: themeColors.primary + '30' }],
                  ]}
                  onPress={() => handleDateSelect(currentMonth.year, currentMonth.month, day)}
                  activeOpacity={0.7}
                >
                  <Text
                    style={[
                      styles.dayText,
                      { color: themeColors.text },
                      isSelected && { color: '#FFFFFF', fontWeight: '600' },
                      isTodayDate && !isSelected && { color: themeColors.primary, fontWeight: '600' },
                    ]}
                  >
                    {day}
                  </Text>
                </TouchableOpacity>
              );
            })}
          </ScrollView>
        </View>
        
        {/* Action Buttons */}
        <View style={styles.buttonContainer}>
          <TouchableOpacity
            style={[styles.button, styles.cancelButton, { 
              borderColor: themeColors.border,
              backgroundColor: themeColors.card,
              ...Platform.select({
                ios: {
                  shadowColor: '#000',
                  shadowOffset: { width: 0, height: 2 },
                  shadowOpacity: 0.1,
                  shadowRadius: 4,
                },
                android: {
                  elevation: 3,
                },
              }),
            }]}
            onPress={onCancel}
            activeOpacity={0.8}
          >
            <Text style={[styles.buttonText, { color: themeColors.text }]}>Cancel</Text>
          </TouchableOpacity>
          
          <TouchableOpacity
            style={[styles.button, styles.saveButton, { 
              backgroundColor: themeColors.primary,
              ...Platform.select({
                ios: {
                  shadowColor: themeColors.primary,
                  shadowOffset: { width: 0, height: 4 },
                  shadowOpacity: 0.3,
                  shadowRadius: 8,
                },
                android: {
                  elevation: 6,
                },
              }),
            }]}
            onPress={handleSave}
            activeOpacity={0.8}
          >
            <Text style={[styles.buttonText, { color: '#FFFFFF', fontWeight: '600' }]}>Apply</Text>
          </TouchableOpacity>
        </View>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  overlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: 'flex-end',
    zIndex: 1000,
  },
  container: {
    borderTopLeftRadius: 24,
    borderTopRightRadius: 24,
    padding: 20,
    maxHeight: screenHeight * 0.8,
    ...Platform.select({
      ios: {
        shadowColor: '#000',
        shadowOffset: { width: 0, height: -4 },
        shadowOpacity: 0.15,
        shadowRadius: 12,
      },
      android: {
        elevation: 8,
      },
    }),
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 20,
  },
  headerLeft: {
    flex: 1,
  },
  title: {
    fontSize: 20,
    fontWeight: '700',
    marginBottom: 4,
  },
  subtitle: {
    fontSize: 14,
  },
  closeButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    justifyContent: 'center',
    alignItems: 'center',
    marginLeft: 16,
  },
  dateDisplay: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 20,
  },
  dateItem: {
    flex: 1,
  },
  dateLabel: {
    fontSize: 12,
    marginBottom: 6,
    fontWeight: '500',
  },
  dateValue: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 10,
    borderRadius: 10,
  },
  dateIcon: {
    marginRight: 6,
  },
  dateText: {
    fontSize: 14,
    fontWeight: '500',
  },
  dateSeparator: {
    paddingHorizontal: 12,
  },
  dateSeparatorText: {
    fontSize: 14,
    fontWeight: '500',
  },
  monthNavigation: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  navButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    ...Platform.select({
      ios: {
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 4,
      },
      android: {
        elevation: 2,
      },
    }),
  },
  monthName: {
    fontSize: 18,
    fontWeight: '600',
  },
  calendarContainer: {
    marginBottom: 20,
  },
  weekdayHeader: {
    flexDirection: 'row',
    marginBottom: 8,
  },
  weekdayText: {
    flex: 1,
    textAlign: 'center',
    fontSize: 12,
    fontWeight: '600',
    paddingVertical: 8,
  },
  daysScrollContainer: {
    maxHeight: 240,
  },
  daysContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  dayButton: {
    width: '14.28%',
    aspectRatio: 1,
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 20,
    marginVertical: 2,
    minHeight: 40,
  },
  selectedDay: {
    ...Platform.select({
      ios: {
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.2,
        shadowRadius: 4,
      },
      android: {
        elevation: 3,
      },
    }),
  },
  rangeDay: {},
  dayText: {
    fontSize: 14,
    fontWeight: '500',
  },
  emptyDay: {
    width: '14.28%',
    aspectRatio: 1,
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    gap: 12,
  },
  button: {
    flex: 1,
    paddingVertical: 14,
    paddingHorizontal: 20,
    borderRadius: 12,
    alignItems: 'center',
    justifyContent: 'center',
  },
  cancelButton: {
    borderWidth: 1,
  },
  saveButton: {},
  buttonText: {
    fontSize: 16,
    fontWeight: '500',
  },
});