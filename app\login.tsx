import React, { useState } from 'react';
import { View, Text, StyleSheet, Image, TouchableOpacity, ActivityIndicator, Platform } from 'react-native';
import { useRouter, Stack } from 'expo-router';
import { StatusBar } from 'expo-status-bar';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useTheme } from '@/context/ThemeContext';
import { useAuth } from '@/context/AuthContext';
import { AntDesign } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';

export default function LoginScreen() {
  const { colors } = useTheme();
  const router = useRouter();
  const { signInWithGoogle, signInWithApple, isLoading } = useAuth();
  const [error, setError] = useState<string | null>(null);

  const handleGoogleSignIn = async () => {
    try {
      setError(null);
      const success = await signInWithGoogle();
      if (success) {
        router.replace('/(tabs)');
      }
    } catch (err) {
      setError('Failed to sign in with Google. Please try again.');
      console.error('Google sign in error:', err);
    }
  };

  const handleAppleSignIn = async () => {
    try {
      setError(null);
      const success = await signInWithApple();
      if (success) {
        router.replace('/(tabs)');
      }
    } catch (err) {
      setError('Failed to sign in with Apple. Please try again.');
      console.error('Apple sign in error:', err);
    }
  };

  return (
    <>
      <StatusBar style={colors.statusBar} />
      <Stack.Screen options={{ headerShown: false }} />
      <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
        <LinearGradient
          colors={[colors.primary + '20', colors.background]}
          style={styles.gradientBackground}
        />
        
        <View style={styles.logoContainer}>
          <Image 
            source={{ uri: 'https://images.unsplash.com/photo-1633158829585-23ba8f7c8caf?q=80&w=2070&auto=format&fit=crop' }} 
            style={styles.logoImage}
          />
          <Text style={[styles.appName, { color: colors.text }]}>Expense Tracker</Text>
          <Text style={[styles.tagline, { color: colors.textSecondary }]}>
            Manage your finances with ease
          </Text>
        </View>

        <View style={styles.authContainer}>
          {error && (
            <View style={[styles.errorContainer, { backgroundColor: colors.error + '20' }]}>
              <Text style={[styles.errorText, { color: colors.error }]}>{error}</Text>
            </View>
          )}

          <TouchableOpacity
            style={[styles.authButton, { backgroundColor: colors.card }]}
            onPress={handleGoogleSignIn}
            disabled={isLoading}
          >
            <AntDesign name="google" size={20} color="#DB4437" />
            <Text style={[styles.authButtonText, { color: colors.text }]}>
              {isLoading ? 'Signing in...' : 'Continue with Google'}
            </Text>
            {isLoading && <ActivityIndicator size="small" color={colors.primary} style={styles.loader} />}
          </TouchableOpacity>

          {Platform.OS === 'ios' && (
            <TouchableOpacity
              style={[styles.authButton, { backgroundColor: colors.card, marginTop: 16 }]}
              onPress={handleAppleSignIn}
              disabled={isLoading}
            >
              <AntDesign name="apple1" size={20} color={colors.text} />
              <Text style={[styles.authButtonText, { color: colors.text }]}>
                {isLoading ? 'Signing in...' : 'Continue with Apple'}
              </Text>
              {isLoading && <ActivityIndicator size="small" color={colors.primary} style={styles.loader} />}
            </TouchableOpacity>
          )}

          <TouchableOpacity 
            style={styles.skipButton}
            onPress={() => router.replace('/(tabs)')}
          >
            <Text style={[styles.skipButtonText, { color: colors.textSecondary }]}>
              Skip for now
            </Text>
          </TouchableOpacity>
        </View>

        <View style={styles.footer}>
          <Text style={[styles.footerText, { color: colors.textSecondary }]}>
            By continuing, you agree to our{' '}
            <Text style={[styles.footerLink, { color: colors.primary }]}>Terms of Service</Text> and{' '}
            <Text style={[styles.footerLink, { color: colors.primary }]}>Privacy Policy</Text>
          </Text>
        </View>
      </SafeAreaView>
    </>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'space-between',
    padding: 24,
  },
  gradientBackground: {
    position: 'absolute',
    left: 0,
    right: 0,
    top: 0,
    height: '100%',
  },
  logoContainer: {
    alignItems: 'center',
    marginTop: 60,
  },
  logoImage: {
    width: 100,
    height: 100,
    borderRadius: 20,
  },
  appName: {
    fontSize: 28,
    fontWeight: '700',
    marginTop: 16,
  },
  tagline: {
    fontSize: 16,
    marginTop: 8,
    textAlign: 'center',
  },
  authContainer: {
    width: '100%',
    marginVertical: 40,
  },
  errorContainer: {
    padding: 12,
    borderRadius: 8,
    marginBottom: 16,
  },
  errorText: {
    fontSize: 14,
  },
  authButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 16,
    borderRadius: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  authButtonText: {
    fontSize: 16,
    fontWeight: '600',
    marginLeft: 12,
  },
  loader: {
    marginLeft: 10,
  },
  skipButton: {
    alignItems: 'center',
    marginTop: 24,
    padding: 12,
  },
  skipButtonText: {
    fontSize: 16,
    fontWeight: '500',
  },
  footer: {
    marginBottom: 20,
  },
  footerText: {
    fontSize: 14,
    textAlign: 'center',
  },
  footerLink: {
    fontWeight: '500',
  },
});