import React, { useEffect, useState } from 'react';
import { View, Text, StyleSheet, ScrollView, RefreshControl, TouchableOpacity, ActivityIndicator, Modal } from 'react-native';
import { useTransactionStore } from '@/store/transactionStore';
import { formatCurrency } from '@/utils/formatters';
import SummaryCard from '@/components/SummaryCard';
import TransactionItem from '@/components/TransactionItem';
import EmptyState from '@/components/EmptyState';
import { getCategory } from '@/constants/categories';
import { BarChart2, TrendingUp, Calendar, ArrowUpRight, ArrowDownLeft, AlertTriangle } from 'lucide-react-native';
import { useTheme } from '@/context/ThemeContext';
import { useRouter } from 'expo-router';
import { useToast } from '@/context/ToastContext';
import { useAnalytics } from '@/context/AnalyticsContext';
import { useNetworkStatus } from '@/hooks/useNetworkStatus';
import { StatusBar } from 'expo-status-bar';
import DateRangePicker from '@/components/DateRangePicker';

export default function DashboardScreen() {
  const { transactions, isLoading, deleteTransaction } = useTransactionStore();
  const [refreshing, setRefreshing] = useState(false);
  const [showDatePicker, setShowDatePicker] = useState(false);
  const [dateRange, setDateRange] = useState({
    startDate: new Date(new Date().setMonth(new Date().getMonth() - 1)).toISOString().split('T')[0],
    endDate: new Date().toISOString().split('T')[0],
  });
  const [isDateFiltered, setIsDateFiltered] = useState(false);
  
  const { theme, colors: themeColors } = useTheme();
  const router = useRouter();
  const { showToast } = useToast();
  const { trackScreen } = useAnalytics();
  const { isConnected } = useNetworkStatus();

  useEffect(() => {
    trackScreen('Dashboard');
  }, []);

  const onRefresh = () => {
    setRefreshing(true);
    // Simulate a refresh
    setTimeout(() => {
      setRefreshing(false);
      showToast({
        message: 'Dashboard refreshed',
        type: 'info',
        duration: 2000
      });
    }, 1000);
  };

  const handleDeleteTransaction = async (id: string): Promise<void> => {
    try {
      await deleteTransaction(id);
      showToast({
        message: 'Transaction deleted successfully',
        type: 'success',
        duration: 2000
      });
    } catch (error) {
      console.error('Error deleting transaction:', error);
      showToast({
        message: 'Failed to delete transaction',
        type: 'error',
        duration: 3000
      });
      throw error;
    }
  };

  const handleDateRangeChange = (startDate: string, endDate: string) => {
    setDateRange({ startDate, endDate });
    setIsDateFiltered(true);
    setShowDatePicker(false);
  };

  const clearDateFilter = () => {
    setIsDateFiltered(false);
    setShowDatePicker(false);
  };

  // Filter transactions by date range if date filter is active
  const filteredTransactions = isDateFiltered 
    ? transactions.filter(t => {
        const transactionDate = new Date(t.date).getTime();
        const startDate = new Date(dateRange.startDate).getTime();
        const endDate = new Date(dateRange.endDate).setHours(23, 59, 59, 999); // End of the day
        return transactionDate >= startDate && transactionDate <= endDate;
      })
    : transactions;

  // Calculate summary data
  const totalIncome = filteredTransactions
    .filter(t => t.type === 'income')
    .reduce((sum, t) => sum + t.amount, 0);
  
  const totalExpenses = filteredTransactions
    .filter(t => t.type === 'expense')
    .reduce((sum, t) => sum + t.amount, 0);

  // Get recent transactions (last 5)
  const recentTransactions = [...filteredTransactions]
    .sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime())
    .slice(0, 5);

  // Calculate top spending categories
  const expensesByCategory = filteredTransactions
    .filter(t => t.type === 'expense')
    .reduce((acc, transaction) => {
      const { category, amount } = transaction;
      if (!acc[category]) {
        acc[category] = 0;
      }
      acc[category] += amount;
      return acc;
    }, {} as Record<string, number>);

  const topCategories = Object.entries(expensesByCategory)
    .sort((a, b) => b[1] - a[1])
    .slice(0, 3)
    .map(([categoryId, amount]) => ({
      category: getCategory(categoryId),
      amount,
      percentage: totalExpenses > 0 ? (amount / totalExpenses) * 100 : 0,
    }));

  // Calculate streak (consecutive days with transactions)
  const calculateStreak = () => {
    if (transactions.length === 0) return 0;
    
    // Get all transaction dates and sort them
    const dates = transactions
      .map(t => new Date(t.date).toISOString().split('T')[0])
      .sort()
      .reverse(); // Most recent first
    
    // Remove duplicates
    const uniqueDates = [...new Set(dates)];
    
    // Start with today and count backwards
    const todayStr = new Date().toISOString().split('T')[0];
    let currentDate = new Date(todayStr);
    let streak = 0;
    
    // Check if there's a transaction today
    if (uniqueDates[0] === todayStr) {
      streak = 1;
      
      // Check previous days
      for (let i = 1; i < 100; i++) { // Limit to 100 days to avoid infinite loop
        currentDate.setDate(currentDate.getDate() - 1);
        const dateStr = currentDate.toISOString().split('T')[0];
        
        if (uniqueDates.includes(dateStr)) {
          streak++;
        } else {
          break;
        }
      }
    }
    
    return streak;
  };
  
  const streak = calculateStreak();

  const navigateToAnalytics = () => {
    router.push('/analytics');
  };

  return (
    <>
      <StatusBar style={theme === 'dark' ? 'light' : 'dark'} />
      
      <ScrollView 
        style={[styles.container, { backgroundColor: themeColors.background }]}
        contentContainerStyle={styles.contentContainer}
        refreshControl={
          <RefreshControl 
            refreshing={refreshing} 
            onRefresh={onRefresh}
            tintColor={themeColors.primary}
            colors={[themeColors.primary]}
          />
        }
      >
        {!isConnected && (
          <View style={[styles.offlineBanner, { backgroundColor: themeColors.warning + '30' }]}>
            <AlertTriangle size={16} color={themeColors.warning} />
            <Text style={[styles.offlineText, { color: themeColors.warning }]}>
              You're offline. Changes will sync when you're back online.
            </Text>
          </View>
        )}
        
        <View style={styles.headerContainer}>
          <Text style={[styles.headerTitle, { color: themeColors.text }]}>Dashboard</Text>
          <TouchableOpacity
            style={[styles.dateFilterButton, { borderColor: themeColors.border }]}
            onPress={() => setShowDatePicker(true)}
          >
            <Calendar size={16} color={themeColors.primary} />
            <Text style={[styles.dateFilterText, { color: themeColors.text }]}>
              {isDateFiltered 
                ? `${new Date(dateRange.startDate).toLocaleDateString().split('/').slice(0, 2).join('/')} - ${new Date(dateRange.endDate).toLocaleDateString().split('/').slice(0, 2).join('/')}` 
                : 'Filter by date'}
            </Text>
          </TouchableOpacity>
        </View>
        
        {isDateFiltered && (
          <View style={[styles.dateFilterBanner, { backgroundColor: themeColors.primary + '15' }]}>
            <Text style={[styles.dateRangeText, { color: themeColors.text }]}>
              Showing data from {new Date(dateRange.startDate).toLocaleDateString()} to {new Date(dateRange.endDate).toLocaleDateString()}
            </Text>
            <TouchableOpacity onPress={clearDateFilter}>
              <Text style={[styles.clearFilterText, { color: themeColors.primary }]}>Clear</Text>
            </TouchableOpacity>
          </View>
        )}
        
        <SummaryCard income={totalIncome} expenses={totalExpenses} />
        
        {/* Tracking streak card */}
        {streak > 0 && (
          <View style={[styles.streakCard, { backgroundColor: themeColors.card }]}>
            <View style={styles.streakHeader}>
              <Text style={[styles.streakTitle, { color: themeColors.text }]}>
                Tracking Streak
              </Text>
              <View style={[styles.streakBadge, { backgroundColor: themeColors.primary + '20' }]}>
                <Text style={[styles.streakBadgeText, { color: themeColors.primary }]}>
                  {streak} {streak === 1 ? 'day' : 'days'}
                </Text>
              </View>
            </View>
            <Text style={[styles.streakMessage, { color: themeColors.textLight }]}>
              {streak === 1 
                ? "You've tracked your finances today. Keep it up!" 
                : `You've tracked your finances for ${streak} consecutive days. Great job!`}
            </Text>
          </View>
        )}
        
        <View style={styles.section}>
          <View style={styles.sectionHeader}>
            <Text style={[styles.sectionTitle, { color: themeColors.text }]}>Recent Transactions</Text>
            
            <TouchableOpacity 
              style={[styles.viewAllButton, { backgroundColor: themeColors.primary + '20' }]}
              onPress={() => router.push('/(tabs)/transactions')}
              accessibilityLabel="View all transactions"
              accessibilityRole="button"
            >
              <Text style={[styles.viewAllText, { color: themeColors.primary }]}>View All</Text>
            </TouchableOpacity>
          </View>
          
          {isLoading ? (
            <View style={styles.loadingContainer}>
              <ActivityIndicator size="large" color={themeColors.primary} />
            </View>
          ) : recentTransactions.length > 0 ? (
            recentTransactions.map(transaction => (
              <TransactionItem 
                key={transaction.id} 
                transaction={transaction}
                onDelete={handleDeleteTransaction}
              />
            ))
          ) : (
            <EmptyState 
              title="No transactions yet"
              message={isDateFiltered ? "No transactions in the selected date range." : "Add your first transaction to see it here."}
              icon={<TrendingUp size={48} color={themeColors.primary} />}
            />
          )}
        </View>
        
        {topCategories.length > 0 && (
          <View style={styles.section}>
            <View style={styles.sectionHeader}>
              <Text style={[styles.sectionTitle, { color: themeColors.text }]}>Top Spending Categories</Text>
              
              <TouchableOpacity 
                style={[styles.viewAllButton, { backgroundColor: themeColors.primary + '20' }]}
                onPress={navigateToAnalytics}
                accessibilityLabel="View analytics"
                accessibilityRole="button"
              >
                <Text style={[styles.viewAllText, { color: themeColors.primary }]}>Analytics</Text>
              </TouchableOpacity>
            </View>
            
            <View style={[styles.categoriesCard, { backgroundColor: themeColors.card }]}>
              {topCategories.map(({ category, amount, percentage }) => (
                <View key={category.id} style={styles.categoryRow}>
                  <View style={styles.categoryInfo}>
                    <View 
                      style={[
                        styles.categoryDot, 
                        { backgroundColor: category.color }
                      ]} 
                    />
                    <Text style={[styles.categoryName, { color: themeColors.text }]}>{category.name}</Text>
                  </View>
                  
                  <View style={styles.categoryValues}>
                    <Text style={[styles.categoryAmount, { color: themeColors.text }]}>
                      {formatCurrency(amount)}
                    </Text>
                    <Text style={[styles.categoryPercentage, { color: themeColors.textLight }]}>
                      {percentage.toFixed(0)}%
                    </Text>
                  </View>
                </View>
              ))}
            </View>
          </View>
        )}
        
        {/* Quick actions card */}
        <View style={[styles.quickActionsCard, { backgroundColor: themeColors.card }]}>
          <Text style={[styles.quickActionsTitle, { color: themeColors.text }]}>
            Quick Actions
          </Text>
          
          <View style={styles.quickActionsGrid}>
            <TouchableOpacity 
              style={[styles.quickActionButton, { backgroundColor: themeColors.expense + '15' }]}
              onPress={() => router.push('/(tabs)/add')}
            >
              <View style={[styles.quickActionIcon, { backgroundColor: themeColors.expense + '30' }]}>
                <ArrowDownLeft size={20} color={themeColors.expense} />
              </View>
              <Text style={[styles.quickActionText, { color: themeColors.text }]}>Add Expense</Text>
            </TouchableOpacity>
            
            <TouchableOpacity 
              style={[styles.quickActionButton, { backgroundColor: themeColors.income + '15' }]}
              onPress={() => {
                router.push({
                  pathname: '/(tabs)/add',
                  params: { type: 'income' }
                });
              }}
            >
              <View style={[styles.quickActionIcon, { backgroundColor: themeColors.income + '30' }]}>
                <ArrowUpRight size={20} color={themeColors.income} />
              </View>
              <Text style={[styles.quickActionText, { color: themeColors.text }]}>Add Income</Text>
            </TouchableOpacity>
            
            <TouchableOpacity 
              style={[styles.quickActionButton, { backgroundColor: themeColors.primary + '15' }]}
              onPress={() => router.push('/(tabs)/analytics')}
            >
              <View style={[styles.quickActionIcon, { backgroundColor: themeColors.primary + '30' }]}>
                <BarChart2 size={20} color={themeColors.primary} />
              </View>
              <Text style={[styles.quickActionText, { color: themeColors.text }]}>View Analytics</Text>
            </TouchableOpacity>
            
            <TouchableOpacity 
              style={[styles.quickActionButton, { backgroundColor: themeColors.primary + '15' }]}
              onPress={() => router.push('/(tabs)/ai-chat')}
            >
              <View style={[styles.quickActionIcon, { backgroundColor: themeColors.primary + '30' }]}>
                <TrendingUp size={20} color={themeColors.primary} />
              </View>
              <Text style={[styles.quickActionText, { color: themeColors.text }]}>AI Assistant</Text>
            </TouchableOpacity>
          </View>
        </View>
      </ScrollView>

      {/* Date Range Picker Modal */}
      <Modal
        visible={showDatePicker}
        transparent={true}
        animationType="fade"
        onRequestClose={() => setShowDatePicker(false)}
      >
        <DateRangePicker
          initialStartDate={dateRange.startDate}
          initialEndDate={dateRange.endDate}
          onSave={handleDateRangeChange}
          onCancel={() => setShowDatePicker(false)}
        />
      </Modal>
    </>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  contentContainer: {
    paddingTop: 16,
    paddingBottom: 32,
  },
  offlineBanner: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 8,
    marginHorizontal: 16,
    borderRadius: 8,
    marginBottom: 8,
  },
  offlineText: {
    fontSize: 14,
    fontWeight: '500',
    marginLeft: 8,
  },
  headerContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    marginBottom: 16,
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: '700',
  },
  dateFilterButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
    borderWidth: 1,
  },
  dateFilterText: {
    fontSize: 12,
    marginLeft: 6,
  },
  dateFilterBanner: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 8,
    marginHorizontal: 16,
    borderRadius: 8,
    marginBottom: 16,
  },
  dateRangeText: {
    fontSize: 12,
    flex: 1,
  },
  clearFilterText: {
    fontSize: 12,
    fontWeight: '600',
    marginLeft: 8,
  },
  section: {
    marginBottom: 24,
    paddingHorizontal: 16,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
  },
  viewAllButton: {
    paddingVertical: 6,
    paddingHorizontal: 12,
    borderRadius: 16,
  },
  viewAllText: {
    fontSize: 14,
    fontWeight: '500',
  },
  categoriesCard: {
    borderRadius: 16,
    padding: 16,
    shadowColor: 'rgba(0, 0, 0, 0.1)',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  categoryRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0, 0, 0, 0.05)',
  },
  categoryInfo: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  categoryDot: {
    width: 12,
    height: 12,
    borderRadius: 6,
    marginRight: 8,
  },
  categoryName: {
    fontSize: 14,
  },
  categoryValues: {
    alignItems: 'flex-end',
  },
  categoryAmount: {
    fontSize: 14,
    fontWeight: '600',
    marginBottom: 2,
  },
  categoryPercentage: {
    fontSize: 12,
  },
  loadingContainer: {
    padding: 40,
    alignItems: 'center',
    justifyContent: 'center',
  },
  streakCard: {
    marginHorizontal: 16,
    marginBottom: 24,
    borderRadius: 16,
    padding: 16,
    shadowColor: 'rgba(0, 0, 0, 0.1)',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  streakHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  streakTitle: {
    fontSize: 16,
    fontWeight: '600',
  },
  streakBadge: {
    paddingVertical: 4,
    paddingHorizontal: 8,
    borderRadius: 12,
  },
  streakBadgeText: {
    fontSize: 14,
    fontWeight: '600',
  },
  streakMessage: {
    fontSize: 14,
  },
  quickActionsCard: {
    marginHorizontal: 16,
    marginBottom: 24,
    borderRadius: 16,
    padding: 16,
    shadowColor: 'rgba(0, 0, 0, 0.1)',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  quickActionsTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 16,
  },
  quickActionsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  quickActionButton: {
    width: '48%',
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    alignItems: 'center',
  },
  quickActionIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 8,
  },
  quickActionText: {
    fontSize: 14,
    fontWeight: '500',
  },
});