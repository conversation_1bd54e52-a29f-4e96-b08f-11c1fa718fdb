import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { useTheme } from '@/context/ThemeContext';
import { AlertCircle } from 'lucide-react-native';

interface PieChartData {
  label: string;
  value: number;
  color: string;
}

interface BarChartData {
  label: string;
  values: {
    value: number;
    color: string;
    label: string;
  }[];
}

interface ChartFallbackProps {
  type?: 'pie' | 'bar';
  data?: PieChartData[] | BarChartData[];
  message?: string;
  iconColor?: string;
  textColor?: string;
}

export default function ChartFallback({ 
  type, 
  data, 
  message = "Chart visualization not available",
  iconColor,
  textColor
}: ChartFallbackProps) {
  const { colors: themeColors } = useTheme();
  
  if (type === 'pie' && data) {
    const pieData = data as PieChartData[];
    const total = pieData.reduce((sum, item) => sum + item.value, 0);
    
    return (
      <View style={styles.container}>
        <View style={styles.pieContainer}>
          <View style={styles.pieChart}>
            {pieData.map((item, index) => {
              const percentage = (item.value / total) * 100;
              return (
                <View key={index} style={styles.pieItem}>
                  <View style={[styles.colorBox, { backgroundColor: item.color }]} />
                  <Text style={[styles.pieLabel, { color: themeColors.text }]}>
                    {item.label}
                  </Text>
                  <Text style={[styles.pieValue, { color: themeColors.textLight }]}>
                    {percentage.toFixed(1)}%
                  </Text>
                </View>
              );
            })}
          </View>
        </View>
      </View>
    );
  }
  
  if (type === 'bar' && data) {
    const barData = data as BarChartData[];
    
    return (
      <View style={styles.container}>
        <View style={styles.barContainer}>
          {barData.map((item, index) => (
            <View key={index} style={styles.barGroup}>
              <Text style={[styles.barLabel, { color: themeColors.textLight }]}>
                {item.label}
              </Text>
              <View style={styles.barValues}>
                {item.values.map((value, valueIndex) => (
                  <View key={valueIndex} style={styles.barValueContainer}>
                    <View 
                      style={[
                        styles.barValue, 
                        { 
                          backgroundColor: value.color,
                          width: `${Math.min(100, value.value / 10)}%`,
                          minWidth: 20
                        }
                      ]}
                    />
                    <Text style={[styles.barValueLabel, { color: themeColors.text }]}>
                      {value.label}: {value.value.toFixed(0)}
                    </Text>
                  </View>
                ))}
              </View>
            </View>
          ))}
        </View>
      </View>
    );
  }
  
  // Fallback message display
  return (
    <View style={[styles.fallbackContainer, { backgroundColor: themeColors.card }]}>
      <AlertCircle size={24} color={iconColor || themeColors.textLight} style={styles.fallbackIcon} />
      <Text style={[styles.fallbackText, { color: textColor || themeColors.textLight }]}>
        {message}
      </Text>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    padding: 16,
  },
  fallbackContainer: {
    padding: 32,
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: 8,
    gap: 12,
  },
  fallbackIcon: {
    opacity: 0.7,
  },
  fallbackText: {
    fontSize: 14,
    textAlign: 'center',
  },
  pieContainer: {
    alignItems: 'center',
  },
  pieChart: {
    width: '100%',
  },
  pieItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  colorBox: {
    width: 16,
    height: 16,
    borderRadius: 4,
    marginRight: 8,
  },
  pieLabel: {
    flex: 1,
    fontSize: 14,
  },
  pieValue: {
    fontSize: 14,
    fontWeight: '500',
  },
  barContainer: {
    width: '100%',
  },
  barGroup: {
    marginBottom: 16,
  },
  barLabel: {
    fontSize: 14,
    marginBottom: 4,
  },
  barValues: {},
  barValueContainer: {
    marginBottom: 8,
  },
  barValue: {
    height: 20,
    borderRadius: 4,
    marginBottom: 4,
  },
  barValueLabel: {
    fontSize: 12,
  },
});