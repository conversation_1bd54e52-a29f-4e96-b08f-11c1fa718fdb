import React, { useState, useMemo } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Dimensions,
  Modal,
} from 'react-native';
import { Stack } from 'expo-router';
import { useTransactionStore } from '@/store/transactionStore';
import { formatCurrency } from '@/utils/formatters';
import { useTheme } from '@/context/ThemeContext';
import { StatusBar } from 'expo-status-bar';
import { Calendar, TrendingUp, TrendingDown, PieChart, BarChart3 } from 'lucide-react-native';
import { getCategory } from '@/constants/categories';
import ChartFallback from '@/components/ChartFallback';
import DateRangePicker from '@/components/DateRangePicker';

const { width } = Dimensions.get('window');

export default function AnalyticsScreen() {
  const { transactions } = useTransactionStore();
  const { theme, colors: themeColors } = useTheme();
  const [selectedPeriod, setSelectedPeriod] = useState<'week' | 'month' | 'year' | 'custom'>('month');
  const [showDatePicker, setShowDatePicker] = useState(false);
  const [customDateRange, setCustomDateRange] = useState<{ start: Date; end: Date } | null>(null);

  // Calculate date range based on selected period
  const dateRange = useMemo(() => {
    const now = new Date();
    const start = new Date();
    const end = new Date();

    if (selectedPeriod === 'custom' && customDateRange) {
      return {
        start: customDateRange.start,
        end: customDateRange.end,
      };
    }

    switch (selectedPeriod) {
      case 'week':
        start.setDate(now.getDate() - 7);
        break;
      case 'month':
        start.setMonth(now.getMonth() - 1);
        break;
      case 'year':
        start.setFullYear(now.getFullYear() - 1);
        break;
    }

    return { start, end };
  }, [selectedPeriod, customDateRange]);

  // Filter transactions based on date range
  const filteredTransactions = useMemo(() => {
    return transactions.filter(transaction => {
      const transactionDate = new Date(transaction.date);
      return transactionDate >= dateRange.start && transactionDate <= dateRange.end;
    });
  }, [transactions, dateRange]);

  // Calculate totals
  const totals = useMemo(() => {
    const income = filteredTransactions
      .filter(t => t.type === 'income')
      .reduce((sum, t) => sum + t.amount, 0);
    
    const expenses = filteredTransactions
      .filter(t => t.type === 'expense')
      .reduce((sum, t) => sum + t.amount, 0);
    
    return {
      income,
      expenses,
      net: income - expenses,
    };
  }, [filteredTransactions]);

  // Calculate category breakdown
  const categoryBreakdown = useMemo(() => {
    const breakdown: { [key: string]: { amount: number; count: number; type: 'income' | 'expense' } } = {};
    
    filteredTransactions.forEach(transaction => {
      if (!breakdown[transaction.category]) {
        breakdown[transaction.category] = {
          amount: 0,
          count: 0,
          type: transaction.type,
        };
      }
      breakdown[transaction.category].amount += transaction.amount;
      breakdown[transaction.category].count += 1;
    });
    
    return Object.entries(breakdown)
      .map(([categoryId, data]) => ({
        category: getCategory(categoryId),
        ...data,
      }))
      .sort((a, b) => b.amount - a.amount);
  }, [filteredTransactions]);

  // Calculate daily spending trend
  const dailyTrend = useMemo(() => {
    const dailyData: { [key: string]: { income: number; expenses: number } } = {};
    
    filteredTransactions.forEach(transaction => {
      const date = transaction.date;
      if (!dailyData[date]) {
        dailyData[date] = { income: 0, expenses: 0 };
      }
      
      if (transaction.type === 'income') {
        dailyData[date].income += transaction.amount;
      } else {
        dailyData[date].expenses += transaction.amount;
      }
    });
    
    return Object.entries(dailyData)
      .map(([date, data]) => ({
        date,
        ...data,
        net: data.income - data.expenses,
      }))
      .sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime())
      .slice(-30); // Last 30 days
  }, [filteredTransactions]);

  const handleCustomDateRange = (startDate: string, endDate: string) => {
    const start = new Date(startDate);
    const end = new Date(endDate);
    setCustomDateRange({ start, end });
    setSelectedPeriod('custom');
    setShowDatePicker(false);
  };

  const formatPeriodLabel = () => {
    if (selectedPeriod === 'custom' && customDateRange) {
      return `${customDateRange.start.toLocaleDateString()} - ${customDateRange.end.toLocaleDateString()}`;
    }
    
    switch (selectedPeriod) {
      case 'week':
        return 'Last 7 days';
      case 'month':
        return 'Last 30 days';
      case 'year':
        return 'Last 12 months';
      default:
        return 'Custom range';
    }
  };

  return (
    <>
      <Stack.Screen 
        options={{
          title: 'Analytics',
          headerStyle: { backgroundColor: themeColors.background },
          headerTintColor: themeColors.text,
        }} 
      />
      
      <StatusBar style={theme === 'dark' ? 'light' : 'dark'} />
      
      <ScrollView style={[styles.container, { backgroundColor: themeColors.background }]}>
        {/* Period Selector */}
        <View style={styles.periodSelector}>
          <ScrollView horizontal showsHorizontalScrollIndicator={false} contentContainerStyle={styles.periodButtons}>
            {(['week', 'month', 'year', 'custom'] as const).map((period) => (
              <TouchableOpacity
                key={period}
                style={[
                  styles.periodButton,
                  {
                    backgroundColor: selectedPeriod === period ? themeColors.primary : themeColors.card,
                  },
                ]}
                onPress={() => {
                  if (period === 'custom') {
                    setShowDatePicker(true);
                  } else {
                    setSelectedPeriod(period);
                  }
                }}
              >
                <Text
                  style={[
                    styles.periodButtonText,
                    {
                      color: selectedPeriod === period ? '#FFFFFF' : themeColors.text,
                    },
                  ]}
                >
                  {period === 'custom' ? 'Custom' : period.charAt(0).toUpperCase() + period.slice(1)}
                </Text>
              </TouchableOpacity>
            ))}
          </ScrollView>
        </View>

        {/* Period Label */}
        <View style={styles.periodLabel}>
          <Calendar size={16} color={themeColors.textLight} />
          <Text style={[styles.periodLabelText, { color: themeColors.textLight }]}>
            {formatPeriodLabel()}
          </Text>
        </View>

        {/* Summary Cards */}
        <View style={styles.summaryContainer}>
          <View style={[styles.summaryCard, { backgroundColor: themeColors.card }]}>
            <View style={styles.summaryHeader}>
              <TrendingUp size={20} color={themeColors.income} />
              <Text style={[styles.summaryLabel, { color: themeColors.textLight }]}>Income</Text>
            </View>
            <Text style={[styles.summaryAmount, { color: themeColors.income }]}>
              {formatCurrency(totals.income)}
            </Text>
          </View>

          <View style={[styles.summaryCard, { backgroundColor: themeColors.card }]}>
            <View style={styles.summaryHeader}>
              <TrendingDown size={20} color={themeColors.expense} />
              <Text style={[styles.summaryLabel, { color: themeColors.textLight }]}>Expenses</Text>
            </View>
            <Text style={[styles.summaryAmount, { color: themeColors.expense }]}>
              {formatCurrency(totals.expenses)}
            </Text>
          </View>

          <View style={[styles.summaryCard, { backgroundColor: themeColors.card }]}>
            <View style={styles.summaryHeader}>
              <BarChart3 size={20} color={totals.net >= 0 ? themeColors.income : themeColors.expense} />
              <Text style={[styles.summaryLabel, { color: themeColors.textLight }]}>Net</Text>
            </View>
            <Text style={[
              styles.summaryAmount, 
              { color: totals.net >= 0 ? themeColors.income : themeColors.expense }
            ]}>
              {formatCurrency(totals.net)}
            </Text>
          </View>
        </View>

        {/* Category Breakdown */}
        <View style={[styles.section, { backgroundColor: themeColors.card }]}>
          <View style={styles.sectionHeader}>
            <PieChart size={20} color={themeColors.primary} />
            <Text style={[styles.sectionTitle, { color: themeColors.text }]}>
              Category Breakdown
            </Text>
          </View>

          {categoryBreakdown.length > 0 ? (
            <View style={styles.categoryList}>
              {categoryBreakdown.map((item, index) => (
                <View key={item.category.id} style={styles.categoryItem}>
                  <View style={styles.categoryInfo}>
                    <View style={[styles.categoryDot, { backgroundColor: item.category.color }]} />
                    <Text style={[styles.categoryName, { color: themeColors.text }]}>
                      {item.category.name}
                    </Text>
                    <Text style={[styles.categoryCount, { color: themeColors.textLight }]}>
                      ({item.count} transaction{item.count !== 1 ? 's' : ''})
                    </Text>
                  </View>
                  <Text style={[
                    styles.categoryAmount,
                    { color: item.type === 'income' ? themeColors.income : themeColors.expense }
                  ]}>
                    {formatCurrency(item.amount)}
                  </Text>
                </View>
              ))}
            </View>
          ) : (
            <ChartFallback 
              message="No transactions found for the selected period"
              iconColor={themeColors.textLight}
              textColor={themeColors.textLight}
            />
          )}
        </View>

        {/* Daily Trend */}
        <View style={[styles.section, { backgroundColor: themeColors.card }]}>
          <View style={styles.sectionHeader}>
            <TrendingUp size={20} color={themeColors.primary} />
            <Text style={[styles.sectionTitle, { color: themeColors.text }]}>
              Daily Trend
            </Text>
          </View>

          {dailyTrend.length > 0 ? (
            <ScrollView horizontal showsHorizontalScrollIndicator={false}>
              <View style={styles.trendChart}>
                {dailyTrend.map((day, index) => {
                  const maxAmount = Math.max(...dailyTrend.map(d => Math.max(d.income, d.expenses)));
                  const incomeHeight = maxAmount > 0 ? (day.income / maxAmount) * 100 : 0;
                  const expenseHeight = maxAmount > 0 ? (day.expenses / maxAmount) * 100 : 0;

                  return (
                    <View key={day.date} style={styles.trendDay}>
                      <View style={styles.trendBars}>
                        <View 
                          style={[
                            styles.trendBar,
                            {
                              height: incomeHeight,
                              backgroundColor: themeColors.income,
                            }
                          ]}
                        />
                        <View 
                          style={[
                            styles.trendBar,
                            {
                              height: expenseHeight,
                              backgroundColor: themeColors.expense,
                            }
                          ]}
                        />
                      </View>
                      <Text style={[styles.trendDate, { color: themeColors.textLight }]}>
                        {new Date(day.date).getDate()}
                      </Text>
                    </View>
                  );
                })}
              </View>
            </ScrollView>
          ) : (
            <ChartFallback 
              message="No daily data available for the selected period"
              iconColor={themeColors.textLight}
              textColor={themeColors.textLight}
            />
          )}
        </View>

        {/* Transaction Count */}
        <View style={[styles.section, { backgroundColor: themeColors.card }]}>
          <Text style={[styles.sectionTitle, { color: themeColors.text }]}>
            Transaction Summary
          </Text>
          <View style={styles.transactionSummary}>
            <Text style={[styles.transactionSummaryText, { color: themeColors.textLight }]}>
              Total transactions: {filteredTransactions.length}
            </Text>
            <Text style={[styles.transactionSummaryText, { color: themeColors.textLight }]}>
              Income transactions: {filteredTransactions.filter(t => t.type === 'income').length}
            </Text>
            <Text style={[styles.transactionSummaryText, { color: themeColors.textLight }]}>
              Expense transactions: {filteredTransactions.filter(t => t.type === 'expense').length}
            </Text>
          </View>
        </View>
      </ScrollView>

      {/* Custom Date Range Picker Modal */}
      <Modal
        visible={showDatePicker}
        transparent={true}
        animationType="fade"
        onRequestClose={() => setShowDatePicker(false)}
      >
        <DateRangePicker
          initialStartDate={customDateRange?.start.toISOString().split('T')[0] || new Date().toISOString().split('T')[0]}
          initialEndDate={customDateRange?.end.toISOString().split('T')[0] || new Date().toISOString().split('T')[0]}
          onSave={handleCustomDateRange}
          onCancel={() => setShowDatePicker(false)}
        />
      </Modal>
    </>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
  },
  periodSelector: {
    marginBottom: 16,
  },
  periodButtons: {
    flexDirection: 'row',
    gap: 8,
  },
  periodButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
  },
  periodButtonText: {
    fontSize: 14,
    fontWeight: '500',
  },
  periodLabel: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
    gap: 8,
  },
  periodLabelText: {
    fontSize: 14,
  },
  summaryContainer: {
    flexDirection: 'row',
    gap: 12,
    marginBottom: 24,
  },
  summaryCard: {
    flex: 1,
    padding: 16,
    borderRadius: 12,
  },
  summaryHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
    gap: 8,
  },
  summaryLabel: {
    fontSize: 14,
  },
  summaryAmount: {
    fontSize: 18,
    fontWeight: '600',
  },
  section: {
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
  },
  sectionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
    gap: 8,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
  },
  categoryList: {
    gap: 12,
  },
  categoryItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  categoryInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
    gap: 8,
  },
  categoryDot: {
    width: 12,
    height: 12,
    borderRadius: 6,
  },
  categoryName: {
    fontSize: 14,
    fontWeight: '500',
  },
  categoryCount: {
    fontSize: 12,
  },
  categoryAmount: {
    fontSize: 14,
    fontWeight: '600',
  },
  trendChart: {
    flexDirection: 'row',
    alignItems: 'flex-end',
    height: 120,
    gap: 4,
    paddingHorizontal: 8,
  },
  trendDay: {
    alignItems: 'center',
    width: 24,
  },
  trendBars: {
    flexDirection: 'row',
    alignItems: 'flex-end',
    height: 100,
    gap: 1,
  },
  trendBar: {
    width: 8,
    minHeight: 2,
    borderRadius: 2,
  },
  trendDate: {
    fontSize: 10,
    marginTop: 4,
  },
  transactionSummary: {
    gap: 8,
  },
  transactionSummaryText: {
    fontSize: 14,
  },
});