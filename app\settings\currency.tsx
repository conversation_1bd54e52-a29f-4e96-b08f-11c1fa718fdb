import React, { useState } from 'react';
import { 
  View, 
  Text, 
  StyleSheet, 
  TouchableOpacity, 
  ScrollView,
  TextInput
} from 'react-native';
import { Stack, useRouter } from 'expo-router';
import { currencies, Currency } from '@/constants/currencies';
import { useSettingsStore } from '@/store/settingsStore';
import * as Haptics from 'expo-haptics';
import { Platform } from 'react-native';
import { Search, Check, ArrowLeft } from 'lucide-react-native';
import { useTheme } from '@/context/ThemeContext';

// Group currencies by region
const currencyGroups = {
  'North America': currencies.filter(c => ['USD', 'CAD', 'MXN'].includes(c.code)),
  'South America': currencies.filter(c => ['ARS', 'BRL', 'CLP', 'COP', 'PEN'].includes(c.code)),
  'Europe': currencies.filter(c => ['EUR', 'GBP', 'CHF', 'RUB', 'SEK', 'NOK', 'DKK', 'PLN', 'CZK', 'HUF', 'BGN', 'HRK', 'UAH', 'GEL'].includes(c.code)),
  'Asia': currencies.filter(c => ['JPY', 'CNY', 'HKD', 'TWD', 'KRW', 'SGD', 'INR', 'IDR', 'MYR', 'PHP', 'THB', 'VND', 'LKR', 'BDT'].includes(c.code)),
  'Middle East': currencies.filter(c => ['AED', 'SAR', 'ILS', 'TRY'].includes(c.code)),
  'Africa': currencies.filter(c => ['ZAR', 'EGP', 'NGN', 'KES', 'MAD'].includes(c.code)),
  'Oceania': currencies.filter(c => ['AUD', 'NZD'].includes(c.code)),
};

export default function CurrencyScreen() {
  const router = useRouter();
  const { currencyCode, setCurrency } = useSettingsStore();
  const [searchQuery, setSearchQuery] = useState('');
  const { colors: themeColors } = useTheme();
  
  const handleCurrencySelect = (code: string) => {
    if (Platform.OS !== 'web') {
      Haptics.selectionAsync();
    }
    setCurrency(code);
    router.back();
  };
  
  const filteredCurrencyGroups = searchQuery.trim() 
    ? {
        'Search Results': currencies.filter(c => 
          c.code.toLowerCase().includes(searchQuery.toLowerCase()) || 
          c.name.toLowerCase().includes(searchQuery.toLowerCase())
        )
      }
    : currencyGroups;

  const goBack = () => {
    router.back();
  };
  
  return (
    <>
      <Stack.Screen options={{ 
        title: "Select Currency",
        headerShown: true,
        headerStyle: { backgroundColor: themeColors.background },
        headerTintColor: themeColors.text,
        headerShadowVisible: false,
        headerLeft: () => (
          <TouchableOpacity onPress={goBack} style={styles.backButton}>
            <ArrowLeft size={24} color={themeColors.text} />
          </TouchableOpacity>
        ),
      }} />
      
      <View style={[styles.container, { backgroundColor: themeColors.background }]}>
        <View style={[styles.searchContainer, { backgroundColor: themeColors.card, borderColor: themeColors.border }]}>
          <Search size={20} color={themeColors.textLight} style={styles.searchIcon} />
          <TextInput
            style={[styles.searchInput, { color: themeColors.text }]}
            placeholder="Search currencies..."
            value={searchQuery}
            onChangeText={setSearchQuery}
            placeholderTextColor={themeColors.textLight}
          />
        </View>
        
        <ScrollView contentContainerStyle={styles.scrollContent}>
          {Object.entries(filteredCurrencyGroups).map(([region, regionCurrencies]) => (
            regionCurrencies.length > 0 && (
              <View key={region} style={styles.section}>
                <Text style={[styles.sectionTitle, { color: themeColors.text }]}>{region}</Text>
                
                {regionCurrencies.map((currency) => (
                  <TouchableOpacity
                    key={currency.code}
                    style={[styles.currencyItem, { backgroundColor: themeColors.card }]}
                    onPress={() => handleCurrencySelect(currency.code)}
                  >
                    <View style={styles.currencyInfo}>
                      <Text style={[styles.currencySymbol, { color: themeColors.text }]}>{currency.symbol}</Text>
                      <View>
                        <Text style={[styles.currencyName, { color: themeColors.text }]}>{currency.name}</Text>
                        <Text style={[styles.currencyCode, { color: themeColors.textLight }]}>{currency.code}</Text>
                      </View>
                    </View>
                    
                    {currency.code === currencyCode && (
                      <Check size={20} color={themeColors.primary} />
                    )}
                  </TouchableOpacity>
                ))}
              </View>
            )
          ))}
        </ScrollView>
      </View>
    </>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    margin: 16,
    borderRadius: 12,
    paddingHorizontal: 12,
    borderWidth: 1,
  },
  searchIcon: {
    marginRight: 8,
  },
  searchInput: {
    flex: 1,
    paddingVertical: 12,
    fontSize: 16,
  },
  scrollContent: {
    paddingHorizontal: 16,
    paddingBottom: 32,
  },
  section: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 12,
  },
  currencyItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    borderRadius: 12,
    padding: 16,
    marginBottom: 8,
  },
  currencyInfo: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  currencySymbol: {
    fontSize: 20,
    fontWeight: '600',
    width: 40,
  },
  currencyName: {
    fontSize: 16,
  },
  currencyCode: {
    fontSize: 14,
    marginTop: 2,
  },
  backButton: {
    padding: 8,
  },
});