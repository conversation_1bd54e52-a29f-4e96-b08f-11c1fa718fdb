import { createTRPCRouter } from "./create-context";
import hiRoute from "./routes/example/hi/route";
import googleLoginProcedure from "./routes/auth/google-login";
import appleLoginProcedure from "./routes/auth/apple-login";

export const appRouter = createTRPCRouter({
  example: createTRPCRouter({
    hi: hiRoute,
  }),
  auth: createTRPCRouter({
    googleLogin: googleLoginProcedure,
    appleLogin: appleLoginProcedure,
  }),
});

export type AppRouter = typeof appRouter;